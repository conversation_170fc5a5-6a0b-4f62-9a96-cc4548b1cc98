#!/bin/bash

# Load environment variables from .env.staging file
if [ -f .env.staging ]; then
  export $(grep -v '^#' .env.staging | xargs)
fi

docker buildx build \
  --platform linux/amd64,linux/arm64 \
  --build-arg VITE_CORE_SERVICE_URL=https://staging-backend-core-api.agentq.id \
  --build-arg VITE_BACKEND_URL=https://staging-backend-app.agentq.id \
  --build-arg VITE_AI_SERVICE_URL=https://staging-backend-ai-api.agentq.id \
  --build-arg VITE_WEBSOCKET_URL=wss://staging-websocket-ai-single-test.agentq.id \
  --build-arg VITE_WEBSOCKET_TESTRUN_URL=wss://staging-websocket-ai-test-run.agentq.id \
  -t asia-southeast2-docker.pkg.dev/agentq-464900/agentq/app_frontend_agentq:1.0.0 \
  --push .



# docker buildx build \
#   --platform linux/amd64,linux/arm64 \
#   --build-arg VITE_CORE_SERVICE_URL=http://localhost:3000 \
#   --build-arg VITE_BACKEND_URL=http://localhost:3010 \
#   --build-arg VITE_AI_SERVICE_URL=http://localhost:3001 \
#   --build-arg VITE_WEBSOCKET_URL=ws://localhost:3021 \
#   --build-arg VITE_WEBSOCKET_TESTRUN_URL=ws://localhost:3022 \
#   -t app_frontend_agentq:latest \
#   .
