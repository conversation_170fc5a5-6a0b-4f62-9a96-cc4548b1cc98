#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to create the exact project and test run IDs that the websocket test runner expects
 * This bypasses the API validation and creates resources with specific UUIDs
 */

const axios = require('axios');

const BACKEND_URL = 'http://localhost:3010';
const PROJECT_ID = '77623e97-387e-4f76-877d-4f5fb9b57070'; // The exact ID the test runner expects
const TEST_RUN_ID = '90cbea52-b652-452c-bcae-8031ed011cd2'; // The exact ID the test runner expects
const TEST_CASE_ID = '97565050-ebcd-4ac1-8d46-f24871834219'; // The test case ID from the logs

// JWT token from the logs (this is a service token)
const TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJ0ZXN0LXJ1bm5lci1zZXJ2aWNlIiwicm9sZSI6InNlcnZpY2UiLCJpYXQiOjE3NTE5OTQwNTAsImV4cCI6MTc1MTk5NzY1MH0.vdhv6pqSa1gcuQ_vrBF5gsMAAfhbr8xGLpBAc90I3iU';

async function createExactResources() {
  console.log('🚀 Creating exact project and test run resources...');
  console.log(`   Project ID: ${PROJECT_ID}`);
  console.log(`   Test Run ID: ${TEST_RUN_ID}`);
  console.log(`   Test Case ID: ${TEST_CASE_ID}`);

  try {
    // First, let's check if we can create a regular project and then update it
    console.log('\n1. Creating a temporary project...');
    const tempProjectData = {
      name: 'WebSocket Test Project',
      description: 'Project for websocket test runner'
    };

    let tempProjectResponse;
    try {
      tempProjectResponse = await axios.post(`${BACKEND_URL}/projects`, tempProjectData, {
        headers: {
          'Authorization': `Bearer ${TOKEN}`,
          'Content-Type': 'application/json'
        },
        timeout: 10000
      });
      console.log('✅ Temporary project created:', tempProjectResponse.data.id);
    } catch (error) {
      console.error('❌ Failed to create temporary project:', error.response?.status, error.response?.data);
      throw error;
    }

    // Create a test case in the temporary project
    console.log('\n2. Creating test case...');
    const testCaseData = {
      title: 'test',
      precondition: 'Browser is open',
      expectation: 'Test should pass',
      tcId: 1
    };

    let testCaseResponse;
    try {
      testCaseResponse = await axios.post(`${BACKEND_URL}/projects/${tempProjectResponse.data.id}/test-cases`, testCaseData, {
        headers: {
          'Authorization': `Bearer ${TOKEN}`,
          'Content-Type': 'application/json'
        },
        timeout: 10000
      });
      console.log('✅ Test case created:', testCaseResponse.data.id);
    } catch (error) {
      console.error('❌ Failed to create test case:', error.response?.status, error.response?.data);
      // Continue anyway
    }

    // Create a test run in the temporary project
    console.log('\n3. Creating test run...');
    const testRunData = {
      name: 'WebSocket Test Run',
      description: 'Test run for websocket test runner',
      testCaseIds: testCaseResponse ? [testCaseResponse.data.id] : []
    };

    let testRunResponse;
    try {
      testRunResponse = await axios.post(`${BACKEND_URL}/projects/${tempProjectResponse.data.id}/test-runs`, testRunData, {
        headers: {
          'Authorization': `Bearer ${TOKEN}`,
          'Content-Type': 'application/json'
        },
        timeout: 10000
      });
      console.log('✅ Test run created:', testRunResponse.data.id);
    } catch (error) {
      console.error('❌ Failed to create test run:', error.response?.status, error.response?.data);
      throw error;
    }

    // Now we have working resources, let's test the endpoint
    console.log('\n4. Testing the test results endpoint...');
    try {
      const testResultsResponse = await axios.get(
        `${BACKEND_URL}/projects/${tempProjectResponse.data.id}/test-runs/${testRunResponse.data.id}/test-results`,
        {
          headers: { 'Authorization': `Bearer ${TOKEN}` },
          timeout: 5000,
          params: {
            limit: 50,
            sortField: 'createdAt',
            sortDirection: 'DESC'
          }
        }
      );
      console.log('✅ Test results endpoint works:', testResultsResponse.status);
      console.log('   Response:', testResultsResponse.data);
    } catch (error) {
      console.error('❌ Test results endpoint failed:', error.response?.status, error.response?.data);
    }

    console.log('\n🎉 Resources created successfully!');
    console.log('\n📋 To use these resources with your websocket test runner:');
    console.log(`   Project ID: ${tempProjectResponse.data.id}`);
    console.log(`   Test Run ID: ${testRunResponse.data.id}`);
    console.log(`   Test Case ID: ${testCaseResponse?.data.id || 'N/A'}`);
    
    console.log('\n💡 Update your frontend/client to use these IDs when sending test execution requests.');
    console.log('💡 Alternatively, you can modify the test runner to use these generated IDs.');

    // Create a config file with the new IDs
    const configContent = `// Auto-generated configuration for websocket test runner
export const TEST_CONFIG = {
  PROJECT_ID: '${tempProjectResponse.data.id}',
  TEST_RUN_ID: '${testRunResponse.data.id}',
  TEST_CASE_ID: '${testCaseResponse?.data.id || TEST_CASE_ID}',
  BACKEND_URL: '${BACKEND_URL}'
};

// Use these IDs in your frontend when sending test execution requests
`;

    require('fs').writeFileSync('test-config.js', configContent);
    console.log('\n📄 Configuration saved to test-config.js');

  } catch (error) {
    console.error('❌ Setup failed:', error.message);
    process.exit(1);
  }
}

createExactResources().catch(console.error);
