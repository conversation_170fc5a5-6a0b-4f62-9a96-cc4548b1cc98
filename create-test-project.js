#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to create a test project and test run for the websocket test runner
 */

const axios = require('axios');

const BACKEND_URL = 'http://localhost:3010';
const PROJECT_ID = '77623e97-387e-4f76-877d-4f5fb9b57070'; // The ID that the test runner is expecting
const TEST_RUN_ID = '90cbea52-b652-452c-bcae-8031ed011cd2'; // The ID that the test runner is expecting
const TEST_CASE_ID = '97565050-ebcd-4ac1-8d46-f24871834219'; // The test case ID from the logs

// JWT token from the logs (this is a service token)
const TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJ0ZXN0LXJ1bm5lci1zZXJ2aWNlIiwicm9sZSI6InNlcnZpY2UiLCJpYXQiOjE3NTE5OTQwNTAsImV4cCI6MTc1MTk5NzY1MH0.vdhv6pqSa1gcuQ_vrBF5gsMAAfhbr8xGLpBAc90I3iU';

async function createTestProject() {
  console.log('🚀 Creating test project and test run...');
  console.log('ℹ️ Note: This will create new resources with auto-generated IDs');
  console.log('ℹ️ You will need to update your test runner to use the new IDs');

  let createdProjectId = null;
  let createdTestRunId = null;

  try {
    // Step 1: Create the project (without custom ID)
    console.log('\n1. Creating project...');
    const projectData = {
      name: 'WebSocket Test Project',
      description: 'Project for websocket test runner'
    };

    try {
      const projectResponse = await axios.post(`${BACKEND_URL}/projects`, projectData, {
        headers: {
          'Authorization': `Bearer ${TOKEN}`,
          'Content-Type': 'application/json'
        },
        timeout: 10000
      });
      console.log('✅ Project created:', projectResponse.status);
      createdProjectId = projectResponse.data.id;
      console.log('   Project ID:', createdProjectId);
      console.log('   Project Name:', projectResponse.data.name);
    } catch (projectError) {
      console.error('❌ Failed to create project:', projectError.response?.status, projectError.response?.data);
      throw projectError;
    }

    // Step 2: Create a test case
    console.log('\n2. Creating test case...');
    const testCaseData = {
      title: 'test',
      precondition: 'Browser is open',
      expectation: 'Test should pass',
      tcId: 1
    };

    let createdTestCaseId = null;
    try {
      const testCaseResponse = await axios.post(`${BACKEND_URL}/projects/${createdProjectId}/test-cases`, testCaseData, {
        headers: {
          'Authorization': `Bearer ${TOKEN}`,
          'Content-Type': 'application/json'
        },
        timeout: 10000
      });
      console.log('✅ Test case created:', testCaseResponse.status);
      createdTestCaseId = testCaseResponse.data.id;
      console.log('   Test Case ID:', createdTestCaseId);
    } catch (testCaseError) {
      console.error('❌ Failed to create test case:', testCaseError.response?.status, testCaseError.response?.data);
      // Continue anyway, test run creation might still work
    }

    // Step 3: Create the test run
    console.log('\n3. Creating test run...');
    const testRunData = {
      name: 'WebSocket Test Run',
      description: 'Test run for websocket test runner',
      testCaseIds: createdTestCaseId ? [createdTestCaseId] : []
    };

    try {
      const testRunResponse = await axios.post(`${BACKEND_URL}/projects/${createdProjectId}/test-runs`, testRunData, {
        headers: {
          'Authorization': `Bearer ${TOKEN}`,
          'Content-Type': 'application/json'
        },
        timeout: 10000
      });
      console.log('✅ Test run created:', testRunResponse.status);
      createdTestRunId = testRunResponse.data.id;
      console.log('   Test Run ID:', createdTestRunId);
    } catch (testRunError) {
      console.error('❌ Failed to create test run:', testRunError.response?.status, testRunError.response?.data);
      throw testRunError;
    }

    // Step 4: Verify the setup
    console.log('\n4. Verifying setup...');
    try {
      const verifyResponse = await axios.get(
        `${BACKEND_URL}/projects/${createdProjectId}/test-runs/${createdTestRunId}/test-results`,
        {
          headers: { 'Authorization': `Bearer ${TOKEN}` },
          timeout: 5000,
          params: {
            limit: 50,
            sortField: 'createdAt',
            sortDirection: 'DESC'
          }
        }
      );
      console.log('✅ Setup verified! Test results endpoint is accessible:', verifyResponse.status);
      console.log('   Response:', verifyResponse.data);
    } catch (verifyError) {
      console.error('❌ Setup verification failed:', verifyError.response?.status, verifyError.response?.data);
    }

    console.log('\n🎉 Setup complete!');
    console.log('\n📋 To use these resources with your websocket test runner:');
    console.log(`   Project ID: ${createdProjectId}`);
    console.log(`   Test Run ID: ${createdTestRunId}`);
    console.log('\n💡 Update your frontend/client to use these IDs when sending test execution requests.');

  } catch (error) {
    console.error('❌ Setup failed:', error.message);
    process.exit(1);
  }
}

createTestProject().catch(console.error);
