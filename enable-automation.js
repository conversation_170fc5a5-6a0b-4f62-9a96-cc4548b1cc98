#!/usr/bin/env node

/**
 * <PERSON>ript to enable AgentQ automation for all test cases in a project
 * This will set automationByAgentq: true for all test cases
 */

const axios = require('axios');

// Configuration
const BACKEND_URL = 'http://localhost:3010';
const PROJECT_ID = '1'; // Change this to your project ID
const USERNAME = '<EMAIL>'; // Change this to your username
const PASSWORD = 'password123'; // Change this to your password

async function enableAutomationForAllTestCases() {
  try {
    console.log('🔐 Logging in...');
    
    // Login to get JWT token
    const loginResponse = await axios.post(`${BACKEND_URL}/auth/login`, {
      email: USERNAME,
      password: PASSWORD
    });
    
    const token = loginResponse.data.access_token;
    console.log('✅ Login successful');
    
    // Set up axios with auth header
    const authAxios = axios.create({
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    console.log('📋 Fetching test cases...');
    
    // Get all test cases in the project
    const testCasesResponse = await authAxios.get(`${BACKEND_URL}/projects/${PROJECT_ID}/test-cases`);
    const testCases = testCasesResponse.data.results || testCasesResponse.data;
    
    console.log(`📊 Found ${testCases.length} test cases`);
    
    if (testCases.length === 0) {
      console.log('⚠️ No test cases found in project');
      return;
    }
    
    // Enable automation for each test case
    let successCount = 0;
    let errorCount = 0;
    
    for (const testCase of testCases) {
      try {
        console.log(`🔧 Enabling automation for TC-${testCase.tcId}: ${testCase.title}`);
        
        // Enable AgentQ automation
        await authAxios.patch(`${BACKEND_URL}/projects/${PROJECT_ID}/test-cases/tcId/${testCase.tcId}/automation-by-agentq`, {
          automationByAgentq: true
        });
        
        // Also update test case type to automation if it's not already
        if (testCase.testCaseType !== 'automation') {
          await authAxios.patch(`${BACKEND_URL}/projects/${PROJECT_ID}/test-cases/tcId/${testCase.tcId}/type`, {
            testCaseType: 'automation'
          });
        }
        
        successCount++;
        console.log(`✅ TC-${testCase.tcId} automation enabled`);
        
      } catch (error) {
        errorCount++;
        console.error(`❌ Failed to enable automation for TC-${testCase.tcId}:`, error.response?.data?.message || error.message);
      }
    }
    
    console.log('\n📊 Summary:');
    console.log(`✅ Successfully enabled: ${successCount} test cases`);
    console.log(`❌ Failed: ${errorCount} test cases`);
    console.log(`📋 Total processed: ${testCases.length} test cases`);
    
    if (successCount > 0) {
      console.log('\n🎉 Automation has been enabled! You should now see "Run Test" buttons in the test run detail page.');
      console.log('💡 Refresh the test run detail page to see the changes.');
    }
    
  } catch (error) {
    console.error('❌ Script failed:', error.response?.data?.message || error.message);
    process.exit(1);
  }
}

// Run the script
enableAutomationForAllTestCases();
