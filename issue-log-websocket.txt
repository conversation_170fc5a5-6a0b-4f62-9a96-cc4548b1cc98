🔧 Redis Config Debug: { host: 'localhost', port: 6379, password: '***', db: 3 }
TestRun WebSocket server running on port 3022
🚀 BullMQ Queue Service initialized successfully
🎯 Bull Board Dashboard available at: http://localhost:3022/admin/queues
📊 Queue Stats API available at: http://localhost:3022/api/queue/stats
✅ Queue Service initialized (TestRun)
✅ Bull Board Dashboard initialized successfully (TestRun)
✅ Queue stats API endpoints added (TestRun)
✅ Dashboard Service initialized (TestRun)
✅ Connected to Redis successfully
✅ Redis is ready to accept commands
🟢 Worker is ready to process jobs
🏁 Worker drained - no more jobs to process
Client connected to TestRun server
Received message type: auth
Validating JWT token: eyJhbGci...
JWT validation response status: 200
Authentication successful for JWT token: eyJhbGci..., waiting for commands...
Received message type: execute_test
Test data for: test
🔄 Test job 104 started for API key: 42b3443fb959286a487917e34a70afca83099fbc7b2107717598d6f8c237f7e5
⚠️ No active WebSocket connection found for client client-1751910711863-u7d6qh1ii
🔄 Processing test run job 104 for API key: 42b3443fb959286a487917e34a70afca83099fbc7b2107717598d6f8c237f7e5, client: client-1751910711863-u7d6qh1ii
🔍 Test run job 104 added with status: active
📝 Test run job 104 added to queue for API key: 42b3443fb959286a487917e34a70afca83099fbc7b2107717598d6f8c237f7e5, position: 1
📊 Queue status: 0 active, 0 waiting
🚀 Test run job 104 will start immediately
✅ Test job 104 added to queue for client: client-1751910711863-u7d6qh1ii at position: 1
🔍 Queue stats after adding job: 0 waiting, 1 active, 1 total
🔍 Queue check after adding test run job 104: 0 waiting, 1 active
🚀 Executing test run job 104 for API key: 42b3443fb959286a487917e34a70afca83099fbc7b2107717598d6f8c237f7e5, client: client-1751910711863-u7d6qh1ii (connection-independent)
⚠️ No active WebSocket connection for client-1751910711863-u7d6qh1ii, running test in background mode
No user JWT token provided, using JWT_SECRET fallback
Test data for: test
Using existing test file: /Users/<USER>/Documents/github/AGENTQ-AI/websocket_ai_test_run/tests/master.spec.ts
Executing: npx playwright test "/Users/<USER>/Documents/github/AGENTQ-AI/websocket_ai_test_run/tests/master.spec.ts" --reporter=line
✅ Test run job 104 completed successfully for API key: 42b3443fb959286a487917e34a70afca83099fbc7b2107717598d6f8c237f7e5, client: client-1751910711863-u7d6qh1ii
✅ Test job 104 completed for API key: 42b3443fb959286a487917e34a70afca83099fbc7b2107717598d6f8c237f7e5
⚠️ No active WebSocket connection found for client client-1751910711863-u7d6qh1ii
🏁 Worker drained - no more jobs to process
Test output: Loaded test data for: test
Test output: 🚀 Running 1 test using 1 worker
Test output: [1/1] tests/master.spec.ts:20:5 › test
Test output: tests/master.spec.ts:20:5 › test
Test output: 🔍 JWT Token available: true
Test output: 🔍 JWT Token value: eyJhbGciOiJIUzI1NiIs...
Test output: Verifying JWT token with: http://localhost:3010/auth/verify
Test output: ✅ JWT token verification successful
Test output: 🚀 Starting pure Playwright test execution
Test output: 📋 Executing 3 test steps
Test output: 🌐 🌐 Step: prompt user visit https://www.saucedemo.com/ ✓
Client connected to TestRun server
Received message type: command
[2025-07-07T17:51:54.489Z] Recording completion_input usage of 320 tokens for API key: 42b3443f...f7e5
[2025-07-07T17:51:54.510Z] Successfully recorded 320 completion_input tokens for API key: 42b3443f...f7e5
[2025-07-07T17:51:54.510Z] Recording completion_output usage of 24 tokens for API key: 42b3443f...f7e5
[2025-07-07T17:51:54.516Z] Successfully recorded 24 completion_output tokens for API key: 42b3443f...f7e5
Test output: 🌐 🌐 Step: prompt user fill username "nugi" ✓
Received message type: command
[2025-07-07T17:51:55.820Z] Recording completion_input usage of 1300 tokens for API key: 42b3443f...f7e5
[2025-07-07T17:51:55.830Z] Successfully recorded 1300 completion_input tokens for API key: 42b3443f...f7e5
[2025-07-07T17:51:55.831Z] Recording completion_output usage of 20 tokens for API key: 42b3443f...f7e5
[2025-07-07T17:51:55.837Z] Successfully recorded 20 completion_output tokens for API key: 42b3443f...f7e5
Test output: 🌐 🌐 Step: prompt user fill password "nugi" ✓
Received message type: command
[2025-07-07T17:51:56.765Z] Recording completion_input usage of 1301 tokens for API key: 42b3443f...f7e5
[2025-07-07T17:51:56.771Z] Successfully recorded 1301 completion_input tokens for API key: 42b3443f...f7e5
[2025-07-07T17:51:56.772Z] Recording completion_output usage of 20 tokens for API key: 42b3443f...f7e5
[2025-07-07T17:51:56.777Z] Successfully recorded 20 completion_output tokens for API key: 42b3443f...f7e5
Test output: ✅ All test steps completed successfully
Client disconnected from TestRun server
Test output: ✅ 1 passed (3.6s)
Using auth token for backend authentication
Extracted test duration: 3600ms from log: "✅ 1 passed (3.6s)"
Received testData structure: {
  testCaseId: '97565050-ebcd-4ac1-8d46-f24871834219',
  tcId: '1',
  projectId: '77623e97-387e-4f76-877d-4f5fb9b57070',
  testCaseProjectId: '77623e97-387e-4f76-877d-4f5fb9b57070',
  hasTestCase: true,
  testCaseKeys: [ 'title', 'precondition', 'expectation', 'projectId' ],
  duration: 3600
}
Using projectId: 77623e97-387e-4f76-877d-4f5fb9b57070 from: {
  direct: '77623e97-387e-4f76-877d-4f5fb9b57070',
  fromTestCase: '77623e97-387e-4f76-877d-4f5fb9b57070',
  fromAPI: undefined
}
Saving test run results to http://localhost:3010/projects/77623e97-387e-4f76-877d-4f5fb9b57070/test-runs/90cbea52-b652-452c-bcae-8031ed011cd2/test-results/tcId/1 with JWT auth
Sending PATCH data to backend: {
  "status": "passed",
  "actualResult": "Test passed",
  "notes": "Automated test execution - passed\n\nExecution time: 3600ms\n\nLogs:\nLoaded test data for: test\n🚀 Running 1 test using 1 worker\n[1/1] tests/master.spec.ts:20:5 › test\ntests/master.spec.ts:20:5 › test\n🔍 JWT Token available: true\n🔍 JWT Token value: eyJhbGciOiJIUzI1NiIs...\nVerifying JWT token with: http://localhost:3010/auth/verify\n✅ JWT token verification successful\n🚀 Starting pure Playwright test execution\n📋 Executing 3 test steps\n🌐 🌐 Step: prompt user visit https://www.saucedemo.com/ ✓\n🌐 🌐 Step: prompt user fill username \"nugi\" ✓\n🌐 🌐 Step: prompt user fill password \"nugi\" ✓\n✅ All test steps completed successfully\n✅ 1 passed (3.6s)",
  "logs": [
    "Loaded test data for: test",
    "🚀 Running 1 test using 1 worker",
    "[1/1] tests/master.spec.ts:20:5 › test",
    "tests/master.spec.ts:20:5 › test",
    "🔍 JWT Token available: true",
    "🔍 JWT Token value: eyJhbGciOiJIUzI1NiIs...",
    "Verifying JWT token with: http://localhost:3010/auth/verify",
    "✅ JWT token verification successful",
    "🚀 Starting pure Playwright test execution",
    "📋 Executing 3 test steps",
    "🌐 🌐 Step: prompt user visit https://www.saucedemo.com/ ✓",
    "🌐 🌐 Step: prompt user fill username \"nugi\" ✓",
    "🌐 🌐 Step: prompt user fill password \"nugi\" ✓",
    "✅ All test steps completed successfully",
    "✅ 1 passed (3.6s)",
    "⏱️ Execution time: 3600ms"
  ],
  "duration": 3600,
  "executionTime": 3600
}
Duration being sent: 3600
Logs being sent: [
  'Loaded test data for: test',
  '🚀 Running 1 test using 1 worker',
  '[1/1] tests/master.spec.ts:20:5 › test'
] ... (showing first 3 logs)
Last log (duration): ⏱️ Execution time: 3600ms
Test run results updated for test case: 97565050-ebcd-4ac1-8d46-f24871834219 (tcId: 1) in test run: 90cbea52-b652-452c-bcae-8031ed011cd2
Found video file for upload: /Users/<USER>/Documents/github/AGENTQ-AI/websocket_ai_test_run/test-results/master-test/video.webm
Using auth token for video upload authentication
Failed to upload test video: AxiosError: Request failed with status code 404
    at settle (/Users/<USER>/Documents/github/AGENTQ-AI/websocket_ai_test_run/node_modules/axios/lib/core/settle.js:19:12)
    at IncomingMessage.handleStreamEnd (/Users/<USER>/Documents/github/AGENTQ-AI/websocket_ai_test_run/node_modules/axios/lib/adapters/http.js:599:11)
    at IncomingMessage.emit (node:events:530:35)
    at IncomingMessage.emit (node:domain:489:12)
    at endReadableNT (node:internal/streams/readable:1698:12)
    at processTicksAndRejections (node:internal/process/task_queues:90:21)
    at Axios.request (/Users/<USER>/Documents/github/AGENTQ-AI/websocket_ai_test_run/node_modules/axios/lib/core/Axios.js:45:41)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async Function.uploadTestVideo (/Users/<USER>/Documents/github/AGENTQ-AI/websocket_ai_test_run/src/services/test-runner.ts:999:34)
    at async /Users/<USER>/Documents/github/AGENTQ-AI/websocket_ai_test_run/src/services/test-runner.ts:375:11 {
  code: 'ERR_BAD_REQUEST',
  config: {
    transitional: {
      silentJSONParsing: true,
      forcedJSONParsing: true,
      clarifyTimeoutError: false
    },
    adapter: [ 'xhr', 'http', 'fetch' ],
    transformRequest: [ [Function: transformRequest] ],
    transformResponse: [ [Function: transformResponse] ],
    timeout: 5000,
    xsrfCookieName: 'XSRF-TOKEN',
    xsrfHeaderName: 'X-XSRF-TOKEN',
    maxContentLength: -1,
    maxBodyLength: -1,
    env: { FormData: [Function [FormData]], Blob: [class Blob] },
    validateStatus: [Function: validateStatus],
    headers: Object [AxiosHeaders] {
      Accept: 'application/json, text/plain, */*',
      'Content-Type': undefined,
      Authorization: 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJ0ZXN0LXJ1bm5lci1zZXJ2aWNlIiwicm9sZSI6InNlcnZpY2UiLCJpYXQiOjE3NTE5MTA3MTIsImV4cCI6MTc1MTkxNDMxMn0.IcxMU2GLl4xaJGfrBjec6dWmc6YRriUCopy_xaqbFq0',
      'User-Agent': 'axios/1.10.0',
      'Accept-Encoding': 'gzip, compress, deflate, br'
    },
    method: 'get',
    url: 'http://localhost:3010/projects/77623e97-387e-4f76-877d-4f5fb9b57070/test-runs/90cbea52-b652-452c-bcae-8031ed011cd2/test-results',
    allowAbsoluteUrls: true,
    data: undefined
  },
  request: <ref *1> ClientRequest {
    _events: [Object: null prototype] {
      abort: [Function (anonymous)],
      aborted: [Function (anonymous)],
      connect: [Function (anonymous)],
      error: [Function (anonymous)],
      socket: [Function (anonymous)],
      timeout: [Function (anonymous)],
      finish: [Function: requestOnFinish]
    },
    _eventsCount: 7,
    _maxListeners: undefined,
    outputData: [],
    outputSize: 0,
    writable: true,
    destroyed: true,
    _last: true,
    chunkedEncoding: false,
    shouldKeepAlive: true,
    maxRequestsOnConnectionReached: false,
    _defaultKeepAlive: true,
    useChunkedEncodingByDefault: false,
    sendDate: false,
    _removedConnection: false,
    _removedContLen: false,
    _removedTE: false,
    strictContentLength: false,
    _contentLength: 0,
    _hasBody: true,
    _trailer: '',
    finished: true,
    _headerSent: true,
    _closed: true,
    _header: 'GET /projects/77623e97-387e-4f76-877d-4f5fb9b57070/test-runs/90cbea52-b652-452c-bcae-8031ed011cd2/test-results HTTP/1.1\r\n' +
      'Accept: application/json, text/plain, */*\r\n' +
      'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJ0ZXN0LXJ1bm5lci1zZXJ2aWNlIiwicm9sZSI6InNlcnZpY2UiLCJpYXQiOjE3NTE5MTA3MTIsImV4cCI6MTc1MTkxNDMxMn0.IcxMU2GLl4xaJGfrBjec6dWmc6YRriUCopy_xaqbFq0\r\n' +
      'User-Agent: axios/1.10.0\r\n' +
      'Accept-Encoding: gzip, compress, deflate, br\r\n' +
      'Host: localhost:3010\r\n' +
      'Connection: keep-alive\r\n' +
      '\r\n',
    _keepAliveTimeout: 0,
    _onPendingData: [Function: nop],
    agent: Agent {
      _events: [Object: null prototype],
      _eventsCount: 2,
      _maxListeners: undefined,
      defaultPort: 80,
      protocol: 'http:',
      options: [Object: null prototype],
      requests: [Object: null prototype] {},
      sockets: [Object: null prototype] {},
      freeSockets: [Object: null prototype],
      keepAliveMsecs: 1000,
      keepAlive: true,
      maxSockets: Infinity,
      maxFreeSockets: 256,
      scheduling: 'lifo',
      maxTotalSockets: Infinity,
      totalSocketCount: 1,
      [Symbol(shapeMode)]: false,
      [Symbol(kCapture)]: false
    },
    socketPath: undefined,
    method: 'GET',
    maxHeaderSize: undefined,
    insecureHTTPParser: undefined,
    joinDuplicateHeaders: undefined,
    path: '/projects/77623e97-387e-4f76-877d-4f5fb9b57070/test-runs/90cbea52-b652-452c-bcae-8031ed011cd2/test-results',
    _ended: true,
    res: IncomingMessage {
      _events: [Object],
      _readableState: [ReadableState],
      _maxListeners: undefined,
      socket: null,
      httpVersionMajor: 1,
      httpVersionMinor: 1,
      httpVersion: '1.1',
      complete: true,
      rawHeaders: [Array],
      rawTrailers: [],
      joinDuplicateHeaders: undefined,
      aborted: false,
      upgrade: false,
      url: '',
      method: null,
      statusCode: 404,
      statusMessage: 'Not Found',
      client: [Socket],
      _consuming: false,
      _dumped: false,
      req: [Circular *1],
      _eventsCount: 4,
      responseUrl: 'http://localhost:3010/projects/77623e97-387e-4f76-877d-4f5fb9b57070/test-runs/90cbea52-b652-452c-bcae-8031ed011cd2/test-results',
      redirects: [],
      [Symbol(shapeMode)]: true,
      [Symbol(kCapture)]: false,
      [Symbol(kHeaders)]: [Object],
      [Symbol(kHeadersCount)]: 18,
      [Symbol(kTrailers)]: null,
      [Symbol(kTrailersCount)]: 0
    },
    aborted: false,
    timeoutCb: null,
    upgradeOrConnect: false,
    parser: null,
    maxHeadersCount: null,
    reusedSocket: true,
    host: 'localhost',
    protocol: 'http:',
    _redirectable: Writable {
      _events: [Object],
      _writableState: [WritableState],
      _maxListeners: undefined,
      _options: [Object],
      _ended: true,
      _ending: true,
      _redirectCount: 0,
      _redirects: [],
      _requestBodyLength: 0,
      _requestBodyBuffers: [],
      _eventsCount: 3,
      _onNativeResponse: [Function (anonymous)],
      _currentRequest: [Circular *1],
      _currentUrl: 'http://localhost:3010/projects/77623e97-387e-4f76-877d-4f5fb9b57070/test-runs/90cbea52-b652-452c-bcae-8031ed011cd2/test-results',
      _timeout: null,
      [Symbol(shapeMode)]: true,
      [Symbol(kCapture)]: false
    },
    [Symbol(shapeMode)]: false,
    [Symbol(kCapture)]: false,
    [Symbol(kBytesWritten)]: 0,
    [Symbol(kNeedDrain)]: false,
    [Symbol(corked)]: 0,
    [Symbol(kChunkedBuffer)]: [],
    [Symbol(kChunkedLength)]: 0,
    [Symbol(kSocket)]: Socket {
      connecting: false,
      _hadError: false,
      _parent: null,
      _host: 'localhost',
      _closeAfterHandlingError: false,
      _events: [Object],
      _readableState: [ReadableState],
      _writableState: [WritableState],
      allowHalfOpen: false,
      _maxListeners: undefined,
      _eventsCount: 6,
      _sockname: null,
      _pendingData: null,
      _pendingEncoding: '',
      server: null,
      _server: null,
      timeout: 4000,
      parser: null,
      _httpMessage: null,
      autoSelectFamilyAttemptedAddresses: [Array],
      [Symbol(async_id_symbol)]: -1,
      [Symbol(kHandle)]: [TCP],
      [Symbol(lastWriteQueueSize)]: 0,
      [Symbol(timeout)]: Timeout {
        _idleTimeout: 4000,
        _idlePrev: [TimersList],
        _idleNext: [Timeout],
        _idleStart: 9584,
        _onTimeout: [Function: bound ],
        _timerArgs: undefined,
        _repeat: null,
        _destroyed: false,
        [Symbol(refed)]: false,
        [Symbol(kHasPrimitive)]: false,
        [Symbol(asyncId)]: 592,
        [Symbol(triggerId)]: 590,
        [Symbol(kAsyncContextFrame)]: undefined
      },
      [Symbol(kBuffer)]: null,
      [Symbol(kBufferCb)]: null,
      [Symbol(kBufferGen)]: null,
      [Symbol(shapeMode)]: true,
      [Symbol(kCapture)]: false,
      [Symbol(kSetNoDelay)]: true,
      [Symbol(kSetKeepAlive)]: true,
      [Symbol(kSetKeepAliveInitialDelay)]: 1,
      [Symbol(kBytesRead)]: 0,
      [Symbol(kBytesWritten)]: 0
    },
    [Symbol(kOutHeaders)]: [Object: null prototype] {
      accept: [Array],
      authorization: [Array],
      'user-agent': [Array],
      'accept-encoding': [Array],
      host: [Array]
    },
    [Symbol(errored)]: null,
    [Symbol(kHighWaterMark)]: 65536,
    [Symbol(kRejectNonStandardBodyWrites)]: false,
    [Symbol(kUniqueHeaders)]: null
  },
  response: {
    status: 404,
    statusText: 'Not Found',
    headers: Object [AxiosHeaders] {
      'x-powered-by': 'Express',
      vary: 'Origin',
      'access-control-allow-credentials': 'true',
      'content-type': 'application/json; charset=utf-8',
      'content-length': '113',
      etag: 'W/"71-cMaQzB8Ie21BvWyqfXjrEKs1cVc"',
      date: 'Mon, 07 Jul 2025 17:51:57 GMT',
      connection: 'keep-alive',
      'keep-alive': 'timeout=5'
    },
    config: {
      transitional: [Object],
      adapter: [Array],
      transformRequest: [Array],
      transformResponse: [Array],
      timeout: 5000,
      xsrfCookieName: 'XSRF-TOKEN',
      xsrfHeaderName: 'X-XSRF-TOKEN',
      maxContentLength: -1,
      maxBodyLength: -1,
      env: [Object],
      validateStatus: [Function: validateStatus],
      headers: [Object [AxiosHeaders]],
      method: 'get',
      url: 'http://localhost:3010/projects/77623e97-387e-4f76-877d-4f5fb9b57070/test-runs/90cbea52-b652-452c-bcae-8031ed011cd2/test-results',
      allowAbsoluteUrls: true,
      data: undefined
    },
    request: <ref *1> ClientRequest {
      _events: [Object: null prototype],
      _eventsCount: 7,
      _maxListeners: undefined,
      outputData: [],
      outputSize: 0,
      writable: true,
      destroyed: true,
      _last: true,
      chunkedEncoding: false,
      shouldKeepAlive: true,
      maxRequestsOnConnectionReached: false,
      _defaultKeepAlive: true,
      useChunkedEncodingByDefault: false,
      sendDate: false,
      _removedConnection: false,
      _removedContLen: false,
      _removedTE: false,
      strictContentLength: false,
      _contentLength: 0,
      _hasBody: true,
      _trailer: '',
      finished: true,
      _headerSent: true,
      _closed: true,
      _header: 'GET /projects/77623e97-387e-4f76-877d-4f5fb9b57070/test-runs/90cbea52-b652-452c-bcae-8031ed011cd2/test-results HTTP/1.1\r\n' +
        'Accept: application/json, text/plain, */*\r\n' +
        'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJ0ZXN0LXJ1bm5lci1zZXJ2aWNlIiwicm9sZSI6InNlcnZpY2UiLCJpYXQiOjE3NTE5MTA3MTIsImV4cCI6MTc1MTkxNDMxMn0.IcxMU2GLl4xaJGfrBjec6dWmc6YRriUCopy_xaqbFq0\r\n' +
        'User-Agent: axios/1.10.0\r\n' +
        'Accept-Encoding: gzip, compress, deflate, br\r\n' +
        'Host: localhost:3010\r\n' +
        'Connection: keep-alive\r\n' +
        '\r\n',
      _keepAliveTimeout: 0,
      _onPendingData: [Function: nop],
      agent: [Agent],
      socketPath: undefined,
      method: 'GET',
      maxHeaderSize: undefined,
      insecureHTTPParser: undefined,
      joinDuplicateHeaders: undefined,
      path: '/projects/77623e97-387e-4f76-877d-4f5fb9b57070/test-runs/90cbea52-b652-452c-bcae-8031ed011cd2/test-results',
      _ended: true,
      res: [IncomingMessage],
      aborted: false,
      timeoutCb: null,
      upgradeOrConnect: false,
      parser: null,
      maxHeadersCount: null,
      reusedSocket: true,
      host: 'localhost',
      protocol: 'http:',
      _redirectable: [Writable],
      [Symbol(shapeMode)]: false,
      [Symbol(kCapture)]: false,
      [Symbol(kBytesWritten)]: 0,
      [Symbol(kNeedDrain)]: false,
      [Symbol(corked)]: 0,
      [Symbol(kChunkedBuffer)]: [],
      [Symbol(kChunkedLength)]: 0,
      [Symbol(kSocket)]: [Socket],
      [Symbol(kOutHeaders)]: [Object: null prototype],
      [Symbol(errored)]: null,
      [Symbol(kHighWaterMark)]: 65536,
      [Symbol(kRejectNonStandardBodyWrites)]: false,
      [Symbol(kUniqueHeaders)]: null
    },
    data: {
      message: 'Project with ID 77623e97-387e-4f76-877d-4f5fb9b57070 not found',
      error: 'Not Found',
      statusCode: 404
    }
  },
  status: 404
}
Video upload response status: 404
Video upload response data: {
  message: 'Project with ID 77623e97-387e-4f76-877d-4f5fb9b57070 not found',
  error: 'Not Found',
  statusCode: 404
}
Searching for screenshots in directory: /Users/<USER>/Documents/github/AGENTQ-AI/websocket_ai_test_run/test-results
Directory contents: .last-run.json, master-test
File .last-run.json does not match screenshot pattern
Recursing into subdirectory: master-test
Searching for screenshots in directory: /Users/<USER>/Documents/github/AGENTQ-AI/websocket_ai_test_run/test-results/master-test
Directory contents: test-finished-1.png, video.webm
Found matching screenshot file: test-finished-1.png
File video.webm does not match screenshot pattern
Returning 1 screenshot files from /Users/<USER>/Documents/github/AGENTQ-AI/websocket_ai_test_run/test-results/master-test
Returning 1 screenshot files from /Users/<USER>/Documents/github/AGENTQ-AI/websocket_ai_test_run/test-results
Found screenshot files: [
  '/Users/<USER>/Documents/github/AGENTQ-AI/websocket_ai_test_run/test-results/master-test/test-finished-1.png'
]
Checking specific paths: [
  '/Users/<USER>/Documents/github/AGENTQ-AI/websocket_ai_test_run/test-results/master-test/screenshot.png',
  '/Users/<USER>/Documents/github/AGENTQ-AI/websocket_ai_test_run/test-results/master-1/screenshot.png',
  '/Users/<USER>/Documents/github/AGENTQ-AI/websocket_ai_test_run/test-results/master-test/screenshot.png'
]
Combined screenshot files: [
  '/Users/<USER>/Documents/github/AGENTQ-AI/websocket_ai_test_run/test-results/master-test/test-finished-1.png'
]
Found screenshot file for upload: /Users/<USER>/Documents/github/AGENTQ-AI/websocket_ai_test_run/test-results/master-test/test-finished-1.png
Using auth token for screenshot upload authentication
Failed to upload test screenshot: AxiosError: Request failed with status code 404
    at settle (/Users/<USER>/Documents/github/AGENTQ-AI/websocket_ai_test_run/node_modules/axios/lib/core/settle.js:19:12)
    at IncomingMessage.handleStreamEnd (/Users/<USER>/Documents/github/AGENTQ-AI/websocket_ai_test_run/node_modules/axios/lib/adapters/http.js:599:11)
    at IncomingMessage.emit (node:events:530:35)
    at IncomingMessage.emit (node:domain:489:12)
    at endReadableNT (node:internal/streams/readable:1698:12)
    at processTicksAndRejections (node:internal/process/task_queues:90:21)
    at Axios.request (/Users/<USER>/Documents/github/AGENTQ-AI/websocket_ai_test_run/node_modules/axios/lib/core/Axios.js:45:41)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async Function.uploadTestScreenshot (/Users/<USER>/Documents/github/AGENTQ-AI/websocket_ai_test_run/src/services/test-runner.ts:1160:34)
    at async /Users/<USER>/Documents/github/AGENTQ-AI/websocket_ai_test_run/src/services/test-runner.ts:382:11 {
  code: 'ERR_BAD_REQUEST',
  config: {
    transitional: {
      silentJSONParsing: true,
      forcedJSONParsing: true,
      clarifyTimeoutError: false
    },
    adapter: [ 'xhr', 'http', 'fetch' ],
    transformRequest: [ [Function: transformRequest] ],
    transformResponse: [ [Function: transformResponse] ],
    timeout: 5000,
    xsrfCookieName: 'XSRF-TOKEN',
    xsrfHeaderName: 'X-XSRF-TOKEN',
    maxContentLength: -1,
    maxBodyLength: -1,
    env: { FormData: [Function [FormData]], Blob: [class Blob] },
    validateStatus: [Function: validateStatus],
    headers: Object [AxiosHeaders] {
      Accept: 'application/json, text/plain, */*',
      'Content-Type': undefined,
      Authorization: 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJ0ZXN0LXJ1bm5lci1zZXJ2aWNlIiwicm9sZSI6InNlcnZpY2UiLCJpYXQiOjE3NTE5MTA3MTIsImV4cCI6MTc1MTkxNDMxMn0.IcxMU2GLl4xaJGfrBjec6dWmc6YRriUCopy_xaqbFq0',
      'User-Agent': 'axios/1.10.0',
      'Accept-Encoding': 'gzip, compress, deflate, br'
    },
    method: 'get',
    url: 'http://localhost:3010/projects/77623e97-387e-4f76-877d-4f5fb9b57070/test-runs/90cbea52-b652-452c-bcae-8031ed011cd2/test-results',
    allowAbsoluteUrls: true,
    data: undefined
  },
  request: <ref *1> ClientRequest {
    _events: [Object: null prototype] {
      abort: [Function (anonymous)],
      aborted: [Function (anonymous)],
      connect: [Function (anonymous)],
      error: [Function (anonymous)],
      socket: [Function (anonymous)],
      timeout: [Function (anonymous)],
      finish: [Function: requestOnFinish]
    },
    _eventsCount: 7,
    _maxListeners: undefined,
    outputData: [],
    outputSize: 0,
    writable: true,
    destroyed: true,
    _last: true,
    chunkedEncoding: false,
    shouldKeepAlive: true,
    maxRequestsOnConnectionReached: false,
    _defaultKeepAlive: true,
    useChunkedEncodingByDefault: false,
    sendDate: false,
    _removedConnection: false,
    _removedContLen: false,
    _removedTE: false,
    strictContentLength: false,
    _contentLength: 0,
    _hasBody: true,
    _trailer: '',
    finished: true,
    _headerSent: true,
    _closed: true,
    _header: 'GET /projects/77623e97-387e-4f76-877d-4f5fb9b57070/test-runs/90cbea52-b652-452c-bcae-8031ed011cd2/test-results HTTP/1.1\r\n' +
      'Accept: application/json, text/plain, */*\r\n' +
      'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJ0ZXN0LXJ1bm5lci1zZXJ2aWNlIiwicm9sZSI6InNlcnZpY2UiLCJpYXQiOjE3NTE5MTA3MTIsImV4cCI6MTc1MTkxNDMxMn0.IcxMU2GLl4xaJGfrBjec6dWmc6YRriUCopy_xaqbFq0\r\n' +
      'User-Agent: axios/1.10.0\r\n' +
      'Accept-Encoding: gzip, compress, deflate, br\r\n' +
      'Host: localhost:3010\r\n' +
      'Connection: keep-alive\r\n' +
      '\r\n',
    _keepAliveTimeout: 0,
    _onPendingData: [Function: nop],
    agent: Agent {
      _events: [Object: null prototype],
      _eventsCount: 2,
      _maxListeners: undefined,
      defaultPort: 80,
      protocol: 'http:',
      options: [Object: null prototype],
      requests: [Object: null prototype] {},
      sockets: [Object: null prototype] {},
      freeSockets: [Object: null prototype],
      keepAliveMsecs: 1000,
      keepAlive: true,
      maxSockets: Infinity,
      maxFreeSockets: 256,
      scheduling: 'lifo',
      maxTotalSockets: Infinity,
      totalSocketCount: 1,
      [Symbol(shapeMode)]: false,
      [Symbol(kCapture)]: false
    },
    socketPath: undefined,
    method: 'GET',
    maxHeaderSize: undefined,
    insecureHTTPParser: undefined,
    joinDuplicateHeaders: undefined,
    path: '/projects/77623e97-387e-4f76-877d-4f5fb9b57070/test-runs/90cbea52-b652-452c-bcae-8031ed011cd2/test-results',
    _ended: true,
    res: IncomingMessage {
      _events: [Object],
      _readableState: [ReadableState],
      _maxListeners: undefined,
      socket: null,
      httpVersionMajor: 1,
      httpVersionMinor: 1,
      httpVersion: '1.1',
      complete: true,
      rawHeaders: [Array],
      rawTrailers: [],
      joinDuplicateHeaders: undefined,
      aborted: false,
      upgrade: false,
      url: '',
      method: null,
      statusCode: 404,
      statusMessage: 'Not Found',
      client: [Socket],
      _consuming: false,
      _dumped: false,
      req: [Circular *1],
      _eventsCount: 4,
      responseUrl: 'http://localhost:3010/projects/77623e97-387e-4f76-877d-4f5fb9b57070/test-runs/90cbea52-b652-452c-bcae-8031ed011cd2/test-results',
      redirects: [],
      [Symbol(shapeMode)]: true,
      [Symbol(kCapture)]: false,
      [Symbol(kHeaders)]: [Object],
      [Symbol(kHeadersCount)]: 18,
      [Symbol(kTrailers)]: null,
      [Symbol(kTrailersCount)]: 0
    },
    aborted: false,
    timeoutCb: null,
    upgradeOrConnect: false,
    parser: null,
    maxHeadersCount: null,
    reusedSocket: true,
    host: 'localhost',
    protocol: 'http:',
    _redirectable: Writable {
      _events: [Object],
      _writableState: [WritableState],
      _maxListeners: undefined,
      _options: [Object],
      _ended: true,
      _ending: true,
      _redirectCount: 0,
      _redirects: [],
      _requestBodyLength: 0,
      _requestBodyBuffers: [],
      _eventsCount: 3,
      _onNativeResponse: [Function (anonymous)],
      _currentRequest: [Circular *1],
      _currentUrl: 'http://localhost:3010/projects/77623e97-387e-4f76-877d-4f5fb9b57070/test-runs/90cbea52-b652-452c-bcae-8031ed011cd2/test-results',
      _timeout: null,
      [Symbol(shapeMode)]: true,
      [Symbol(kCapture)]: false
    },
    [Symbol(shapeMode)]: false,
    [Symbol(kCapture)]: false,
    [Symbol(kBytesWritten)]: 0,
    [Symbol(kNeedDrain)]: false,
    [Symbol(corked)]: 0,
    [Symbol(kChunkedBuffer)]: [],
    [Symbol(kChunkedLength)]: 0,
    [Symbol(kSocket)]: Socket {
      connecting: false,
      _hadError: false,
      _parent: null,
      _host: 'localhost',
      _closeAfterHandlingError: false,
      _events: [Object],
      _readableState: [ReadableState],
      _writableState: [WritableState],
      allowHalfOpen: false,
      _maxListeners: undefined,
      _eventsCount: 6,
      _sockname: null,
      _pendingData: null,
      _pendingEncoding: '',
      server: null,
      _server: null,
      timeout: 4000,
      parser: null,
      _httpMessage: null,
      autoSelectFamilyAttemptedAddresses: [Array],
      [Symbol(async_id_symbol)]: -1,
      [Symbol(kHandle)]: [TCP],
      [Symbol(lastWriteQueueSize)]: 0,
      [Symbol(timeout)]: Timeout {
        _idleTimeout: 4000,
        _idlePrev: [TimersList],
        _idleNext: [Timeout],
        _idleStart: 9610,
        _onTimeout: [Function: bound ],
        _timerArgs: undefined,
        _repeat: null,
        _destroyed: false,
        [Symbol(refed)]: false,
        [Symbol(kHasPrimitive)]: false,
        [Symbol(asyncId)]: 613,
        [Symbol(triggerId)]: 611,
        [Symbol(kAsyncContextFrame)]: undefined
      },
      [Symbol(kBuffer)]: null,
      [Symbol(kBufferCb)]: null,
      [Symbol(kBufferGen)]: null,
      [Symbol(shapeMode)]: true,
      [Symbol(kCapture)]: false,
      [Symbol(kSetNoDelay)]: true,
      [Symbol(kSetKeepAlive)]: true,
      [Symbol(kSetKeepAliveInitialDelay)]: 1,
      [Symbol(kBytesRead)]: 0,
      [Symbol(kBytesWritten)]: 0
    },
    [Symbol(kOutHeaders)]: [Object: null prototype] {
      accept: [Array],
      authorization: [Array],
      'user-agent': [Array],
      'accept-encoding': [Array],
      host: [Array]
    },
    [Symbol(errored)]: null,
    [Symbol(kHighWaterMark)]: 65536,
    [Symbol(kRejectNonStandardBodyWrites)]: false,
    [Symbol(kUniqueHeaders)]: null
  },
  response: {
    status: 404,
    statusText: 'Not Found',
    headers: Object [AxiosHeaders] {
      'x-powered-by': 'Express',
      vary: 'Origin',
      'access-control-allow-credentials': 'true',
      'content-type': 'application/json; charset=utf-8',
      'content-length': '113',
      etag: 'W/"71-cMaQzB8Ie21BvWyqfXjrEKs1cVc"',
      date: 'Mon, 07 Jul 2025 17:51:57 GMT',
      connection: 'keep-alive',
      'keep-alive': 'timeout=5'
    },
    config: {
      transitional: [Object],
      adapter: [Array],
      transformRequest: [Array],
      transformResponse: [Array],
      timeout: 5000,
      xsrfCookieName: 'XSRF-TOKEN',
      xsrfHeaderName: 'X-XSRF-TOKEN',
      maxContentLength: -1,
      maxBodyLength: -1,
      env: [Object],
      validateStatus: [Function: validateStatus],
      headers: [Object [AxiosHeaders]],
      method: 'get',
      url: 'http://localhost:3010/projects/77623e97-387e-4f76-877d-4f5fb9b57070/test-runs/90cbea52-b652-452c-bcae-8031ed011cd2/test-results',
      allowAbsoluteUrls: true,
      data: undefined
    },
    request: <ref *1> ClientRequest {
      _events: [Object: null prototype],
      _eventsCount: 7,
      _maxListeners: undefined,
      outputData: [],
      outputSize: 0,
      writable: true,
      destroyed: true,
      _last: true,
      chunkedEncoding: false,
      shouldKeepAlive: true,
      maxRequestsOnConnectionReached: false,
      _defaultKeepAlive: true,
      useChunkedEncodingByDefault: false,
      sendDate: false,
      _removedConnection: false,
      _removedContLen: false,
      _removedTE: false,
      strictContentLength: false,
      _contentLength: 0,
      _hasBody: true,
      _trailer: '',
      finished: true,
      _headerSent: true,
      _closed: true,
      _header: 'GET /projects/77623e97-387e-4f76-877d-4f5fb9b57070/test-runs/90cbea52-b652-452c-bcae-8031ed011cd2/test-results HTTP/1.1\r\n' +
        'Accept: application/json, text/plain, */*\r\n' +
        'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJ0ZXN0LXJ1bm5lci1zZXJ2aWNlIiwicm9sZSI6InNlcnZpY2UiLCJpYXQiOjE3NTE5MTA3MTIsImV4cCI6MTc1MTkxNDMxMn0.IcxMU2GLl4xaJGfrBjec6dWmc6YRriUCopy_xaqbFq0\r\n' +
        'User-Agent: axios/1.10.0\r\n' +
        'Accept-Encoding: gzip, compress, deflate, br\r\n' +
        'Host: localhost:3010\r\n' +
        'Connection: keep-alive\r\n' +
        '\r\n',
      _keepAliveTimeout: 0,
      _onPendingData: [Function: nop],
      agent: [Agent],
      socketPath: undefined,
      method: 'GET',
      maxHeaderSize: undefined,
      insecureHTTPParser: undefined,
      joinDuplicateHeaders: undefined,
      path: '/projects/77623e97-387e-4f76-877d-4f5fb9b57070/test-runs/90cbea52-b652-452c-bcae-8031ed011cd2/test-results',
      _ended: true,
      res: [IncomingMessage],
      aborted: false,
      timeoutCb: null,
      upgradeOrConnect: false,
      parser: null,
      maxHeadersCount: null,
      reusedSocket: true,
      host: 'localhost',
      protocol: 'http:',
      _redirectable: [Writable],
      [Symbol(shapeMode)]: false,
      [Symbol(kCapture)]: false,
      [Symbol(kBytesWritten)]: 0,
      [Symbol(kNeedDrain)]: false,
      [Symbol(corked)]: 0,
      [Symbol(kChunkedBuffer)]: [],
      [Symbol(kChunkedLength)]: 0,
      [Symbol(kSocket)]: [Socket],
      [Symbol(kOutHeaders)]: [Object: null prototype],
      [Symbol(errored)]: null,
      [Symbol(kHighWaterMark)]: 65536,
      [Symbol(kRejectNonStandardBodyWrites)]: false,
      [Symbol(kUniqueHeaders)]: null
    },
    data: {
      message: 'Project with ID 77623e97-387e-4f76-877d-4f5fb9b57070 not found',
      error: 'Not Found',
      statusCode: 404
    }
  },
  status: 404
}
Screenshot upload response status: 404
Screenshot upload response data: {
  message: 'Project with ID 77623e97-387e-4f76-877d-4f5fb9b57070 not found',
  error: 'Not Found',
  statusCode: 404
}
Test execution completed successfully
