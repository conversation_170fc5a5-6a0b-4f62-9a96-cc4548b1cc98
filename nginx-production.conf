server {
    listen 80;
    server_name app.agentq.id www.app.agentq.id;
    root /var/www/app_frontend_agentq/dist;
    index index.html;

    # Redirect /signin to /login
    location = /signin {
        return 301 /login;
    }

    # Serve static assets locally
    location /assets/ {
        gzip_static on;
        expires max;
        add_header Cache-Control public;
    }

    # Handle Vue routes
    location ~ ^/(dashboard|login|system-health|projects|auth/external) {
        try_files $uri $uri/ /index.html;
    }

    # Redirect root to /agentq
    location = / {
        return 301 /agentq/;
    }

    # Optional: CORS headers
    add_header Access-Control-Allow-Origin *;
}