# Backend Configuration
BACKEND_URL=https://backend-app.agentq.id

# JWT Configuration
JWT_SECRET=your_jwt_secret_key_change_in_production

# Frontend Environment Variables

# Backend API URL
AGENTQ_API_URL=https://backend-app.agentq.id

# WebSocket Service Configuration
PORT=3022

# LLM Configuration
LLM_PROVIDER=GEMINI
GEMINI_API_KEY=AIzaSyDjHPbanv4-BMUPU_BxUayBS1B3N0M_H40
GEMINI_MODEL=gemini-1.5-flash #gemini-2.5-flash-preview-04-17
OPENAI_API_KEY=your-openai-api-key-here
OPENAI_MODEL=gpt-4o-mini

# Core Service URL (for API key validation)
CORE_SERVICE_URL=https://backend-core-api.agentq.id

#google storage
GCP_PROJECT_ID=orbital-nirvana-447600-j8
GCP_CLIENT_EMAIL=<EMAIL>
GCP_PRIVATE_KEY="-----B<PERSON><PERSON> PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCoB6KYCSJawJvY\nglhGUMXPPUZ1AL64t3dz6VVclBdwAJCkDZunxMVhItnsK6qnTFqO9/qhhaLV7swj\nWl2sDKOLGNgAGQpTiEJ/XcSivFN8jYto6pSiaftVKFlPHVG2G9xY/71UXAVfEb6X\n0Ed7Xq2aoGnfmMUehRo8LP9vzo1mkYMTVTqdkSWfd9yFO34Hbse/gxqJ3sJiv9hK\ndgSoMJFCOX4YEGl0wEXPRvfcfT4k54g09ajTQfkKILL9Vl6y1hvUkP7LTuZPyLCp\nVu7WfJUZofIAcRRlWdJLG7zCiozJte3CjozidXOqnALGI5hZ0ORMnv47MBDAnStr\nfRY2lX6NAgMBAAECggEAEIyP9awQoiI44d4Ulk2mQymkeCmWny1n/8E0vbrS2XPK\nIHORUmNi5+o9ebE4oY7iQb/SriMx981edazqGjk0ck1Zbh5ekf49mRBsfsqlk62C\nftbRGh3uDw5mSBB518HEC0/vdGY2cTfs2ZmTMLy7xHBAJ9wtWJAra/nSk/nNRhNY\nkzSDTQGEfIiG7E05Tn20IKn7FapL1zsYxNJNiR+fzxpfMlCu0xIZ9HXSjmOTMyyq\njgofQYuQlk2x8ReDKBQhTnuw4jSquCDtHc8UO5d0m1GUMzl3MdpRR4XsCJOL6BpC\naR9e0QchrQ/vj4yla9M5m96MqufPQqk206opzGBa0QKBgQDYLNIffBHYq8qyJyMo\n1Bdo30B6TerZMOwE82yp8pAaXl6q+hEkbWQotg2kXY1tz5SRnU4HgKkkswpKOmKm\nTyKShQD6X5o/C0Pn5hx8gijSgIvqTET702s+oz5auoMoze2ItTRbkPY7ebeOaebZ\nZ6t2op9J9iY9ETlHBCpDln3fRwKBgQDG/DQN5zByoMVNE/L8yzQnQaMyn+WtzQY4\nxSxEu8SZNTS87rnKdXm9VYSQFv4idIeQlQlo6UArC3tnRqzm433cBRDPJYXsFoaV\n8M31USqpytBZ5yMQItIOHAZHLJY0Z2qxztQWVTonlBZZn2OFwxvaUaCaCRrE1Uma\nmU9u5UgliwKBgBAxQ0gCdh/BtnwjvFl5kYtUdbLzlSzRokw4o6ITPkILYZHzC0es\nPf1iraDrNQXNr7Y8jzS2qXTLSHEI8Iy+jrtk5Q7cDGS+hkafkPCumzoxUMmrDtvC\nGV1mwuvu3pY23p0H3bUiJZ+M3Xf4bMdKIajQQ5g5rOGUIbM1F6laY3FPAoGBAIgL\nUG+c+9OzNZHgLMZHC+43AyQOjFKKlVFlBUIs+lohNWBi+yV/se8Ps+Vrwu69qx3f\nt6JBrvx/Cizu9jGZoag20mo95lUmhXeoPrgVeUzC/DYrQB69H85DvMsWoc9MUXcl\nyH1IYc1DgxBdWAbRAY7/NgV6ahHfHUaUwOk521zHAoGAemY2gOSXkPB1c8M3//u2\nDXkRMayzqmnPcCGVPutOt0/MJSJZ0/OrxeE70zMM8PEYNuZY8ipb2R765tr8cyK7\nIwUTCEoawxVhzl2Qg3bI4cRpmJxD+LkPFjDbF34BMLqo34HHqIeqqpvprZVmBf4A\neQIefUz5XF6bZFXqSBB28Uw=\n-----END PRIVATE KEY-----\n"
GCP_BUCKET_NAME=agentq

ENABLE_CLOUD_STORAGE=true

# AgentQ Library Authentication
AGENTQ_AUTH_EMAIL=<EMAIL>
AGENTQ_AUTH_PASSWORD=<EMAIL>
AGENTQ_PROJECT_ID=5861f260-f05f-4649-a421-2ac264a0b1d4

# JWT Token for AgentQ Library (will be generated dynamically)
AGENTQ_JWT_TOKEN=

# AgentQ Environment Configuration
NODE_ENV=production #prod   NODE_ENV=production
#AGENTQ_TOKEN=your_token
VITE_WEBSOCKET_URL=wss://websocket-ai-test-run.agentq.id

# Redis Configuration for BullMQ (TestRun)
REDIS_HOST=127.0.0.1
REDIS_PORT=6379
REDIS_PASSWORD=Asdqwe123@Agentq
REDIS_DB=3