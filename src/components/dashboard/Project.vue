<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { useAuthStore } from '../../stores/auth';
import axios from 'axios';
import ConfirmationModal from '../common/ConfirmationModal.vue';
import apiService from '../../services/api.service';

interface Project {
  id: string;
  name: string;
  description: string;
  createdAt: string;
}

const router = useRouter();
const authStore = useAuthStore();
const API_URL = `${(import.meta as any).env.VITE_BACKEND_URL}/projects`;

const projects = ref<Project[]>([]);
const projectName = ref('');
const projectDescription = ref('');
const searchQuery = ref('');
const currentPage = ref(1);
const totalPages = ref(1);
const totalItems = ref(0);
const loading = ref(false);
const error = ref('');
const editingId = ref<string | null>(null);
const showDeleteModal = ref(false);
const deletingProject = ref<Project | null>(null);

// Check if current user can delete projects (superadmin or admin roles)
const canDeleteProjects = computed(() => {
  const userRole = authStore.user?.role;
  return userRole === 'superadmin' || userRole === 'admin';
});

const fetchProjects = async () => {
  try {
    loading.value = true;
    error.value = '';
    
    console.log('Fetching projects...');
    const response = await apiService.getProjects();
    console.log('Projects response:', response.data);
    
    // Add null checks and default values
    if (response.data && Array.isArray(response.data.projects)) {
      projects.value = response.data.projects;
      totalPages.value = response.data.totalPages || 1;
      totalItems.value = response.data.total || 0;
    } else if (response.data && Array.isArray(response.data)) {
      // Handle case where API returns array directly
      projects.value = response.data;
      totalPages.value = 1;
      totalItems.value = response.data.length;
    } else {
      // Set defaults if response structure is unexpected
      projects.value = [];
      totalPages.value = 1;
      totalItems.value = 0;
      console.warn('Unexpected API response structure:', response.data);
    }
  } catch (err) {
    console.error('Error fetching projects:', err);
    if (typeof err === 'object' && err !== null && 'response' in err && (err as any).response?.status === 401) {
      // Redirect to login page for 401
      router.push('/login');
      return;
    }
    // Handle network error (no response or Axios network error)
    if (
      (typeof err === 'object' && err !== null && 'code' in err && (err as any).code === 'ERR_NETWORK') ||
      (typeof err === 'object' && err !== null && !('response' in err))
    ) {
      // Redirect to login page for network errors
      router.push('/login');
      return;
    }
    error.value = (typeof err === 'object' && err !== null && 'response' in err) ? (err as any).response?.data?.message || 'Failed to fetch projects' : 'Failed to fetch projects';
    projects.value = [];
  } finally {
    loading.value = false;
  }
};

const saveProject = async () => {
  try {
    loading.value = true;
    error.value = '';

    const payload = {
      name: projectName.value,
      description: projectDescription.value
    };

    if (editingId.value) {
      await axios.patch(`${API_URL}/${editingId.value}`, payload);
    } else {
      await apiService.createProject(payload);
    }

    await fetchProjects();
    resetForm();
  } catch (err: any) {
    error.value = err.response?.data?.message || 'Failed to save project';
  } finally {
    loading.value = false;
  }
};

const editProject = (project: Project) => {
  editingId.value = project.id;
  projectName.value = project.name;
  projectDescription.value = project.description;
};

const viewProjectDetail = (project: Project) => {
  router.push(`/projects/${project.id}`);
};

const confirmDelete = (project: Project) => {
  deletingProject.value = project;
  showDeleteModal.value = true;
};

const handleDeleteConfirm = async () => {
  if (!deletingProject.value) return;
  
  try {
    loading.value = true;
    error.value = '';
    
    // Use the API service instead of direct axios call
    await apiService.deleteProject(deletingProject.value.id);
    await fetchProjects();
    
    showDeleteModal.value = false;
    deletingProject.value = null;
  } catch (err: any) {
    error.value = err.response?.data?.message || 'Failed to delete project';
  } finally {
    loading.value = false;
  }
};

const handleDeleteCancel = () => {
  showDeleteModal.value = false;
  deletingProject.value = null;
};

const resetForm = () => {
  editingId.value = null;
  projectName.value = '';
  projectDescription.value = '';
};

const cancelEdit = () => {
  resetForm();
};

const handleSearch = () => {
  currentPage.value = 1;
  fetchProjects();
};

const changePage = (page: number) => {
  currentPage.value = page;
  fetchProjects();
};

onMounted(() => {
  fetchProjects();
});
</script>

<template>
  <div class="projects-page">
    <h2>Projects</h2>
    <p class="page-description">Manage your test case generation projects.</p>
    
    <div v-if="error" class="error-message">
      {{ error }}
    </div>
    
    <div class="projects-container">
      <!-- Create/Edit Project Form -->
      <div class="project-form">
        <h3>{{ editingId ? 'Edit' : 'Create' }} Project</h3>
        <div class="form-group">
          <label for="project-name">Project Name</label>
          <input 
            type="text" 
            id="project-name" 
            v-model="projectName"
            required
            placeholder="Enter project name"
            class="form-input"
          />
        </div>
        
        <div class="form-group">
          <label for="project-description">Description</label>
          <textarea 
            id="project-description" 
            v-model="projectDescription"
            placeholder="Enter project description"
            class="form-input"
            rows="3"
          ></textarea>
        </div>
        
        <div class="form-actions">
          <button 
            class="save-button" 
            @click="saveProject"
            :disabled="loading || !projectName"
          >
            {{ editingId ? 'Update' : 'Create' }} Project
          </button>
          <button 
            v-if="editingId" 
            class="cancel-button" 
            @click="cancelEdit"
            :disabled="loading"
          >
            Cancel
          </button>
        </div>
      </div>
      
      <!-- Search Bar -->
      <div class="search-bar">
        <input 
          type="text"
          v-model="searchQuery"
          placeholder="Search projects..."
          class="search-input"
          @keyup.enter="handleSearch"
        />
        <button class="search-button" @click="handleSearch">
          Search
        </button>
      </div>
      
      <!-- Projects Table -->
      <div class="projects-table-container">
        <table class="projects-table">
          <thead>
            <tr>
              <th>Name</th>
              <th>Description</th>
              <th>Created At</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="project in projects" :key="project.id" @click="viewProjectDetail(project)" style="cursor: pointer;"  >
              <td>{{ project.name }}</td>
              <td>{{ project.description }}</td>
              <td>{{ new Date(project.createdAt).toLocaleDateString() }}</td>
              <td class="actions">
                <button 
                  class="action-button edit" 
                  @click.stop="editProject(project)"
                  :disabled="loading"
                >
                  ✏️
                </button>
                <button
                  v-if="canDeleteProjects"
                  class="action-button delete"
                  @click.stop="confirmDelete(project)"
                  :disabled="loading"
                >
                  🗑️
                </button>
              </td>
            </tr>
          </tbody>
        </table>
        
        <!-- Loading State -->
        <div v-if="loading" class="loading-state">
          Loading...
        </div>
        
        <!-- Empty State -->
        <div v-if="!loading && projects.length === 0" class="empty-state">
          No projects found.
        </div>
        
        <!-- Pagination -->
        <div v-if="totalPages > 1" class="pagination">
          <button 
            class="page-button"
            :disabled="currentPage === 1"
            @click="changePage(currentPage - 1)"
          >
            Previous
          </button>
          <span class="page-info">
            Page {{ currentPage }} of {{ totalPages }}
          </span>
          <button 
            class="page-button"
            :disabled="currentPage === totalPages"
            @click="changePage(currentPage + 1)"
          >
            Next
          </button>
        </div>
      </div>
    </div>
    
    <!-- Confirmation Modal -->
    <ConfirmationModal
      :is-open="showDeleteModal"
      title="Delete Project"
      :message="`Are you sure you want to delete the project '${deletingProject?.name}'? This action cannot be undone.`"
      @confirm="handleDeleteConfirm"
      @cancel="handleDeleteCancel"
    />
  </div>
</template>

<style lang="scss" scoped>
.projects-page {
  padding: 20px;
  
  h2 {
    margin-bottom: 8px;
    font-size: 24px;
    color: #374151;
  }
  
  .page-description {
    color: #6b7280;
    margin-bottom: 24px;
  }
}

.error-message {
  background-color: #fee2e2;
  color: #b91c1c;
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 16px;
}

.projects-container {
  background-color: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.project-form {
  margin-bottom: 32px;
  
  h3 {
    margin-bottom: 16px;
    font-size: 18px;
    color: #374151;
  }
}

.form-group {
  margin-bottom: 16px;
  
  label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #374151;
  }
  
  .form-input {
    width: 100%;
    padding: 12px;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    font-size: 16px;
    
    &:focus {
      outline: none;
      border-color: #e94560;
      box-shadow: 0 0 0 2px rgba(233, 69, 96, 0.1);
    }
  }
  
  textarea.form-input {
    resize: vertical;
    min-height: 100px;
  }
}

.form-actions {
  display: flex;
  gap: 12px;
  margin-top: 24px;
  
  button {
    padding: 12px 24px;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
    
    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }
  
  .save-button {
    background-color: #e94560;
    color: white;
    border: none;
    
    &:hover:not(:disabled) {
      background-color: #d63553;
    }
  }
  
  .cancel-button {
    background-color: #f3f4f6;
    color: #374151;
    border: 1px solid #e5e7eb;
    
    &:hover:not(:disabled) {
      background-color: #e5e7eb;
    }
  }
}

.search-bar {
  display: flex;
  gap: 12px;
  margin-bottom: 24px;
  
  .search-input {
    flex: 1;
    padding: 12px;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    font-size: 16px;
    
    &:focus {
      outline: none;
      border-color: #e94560;
      box-shadow: 0 0 0 2px rgba(233, 69, 96, 0.1);
    }
  }
  
  .search-button {
    padding: 12px 24px;
    background-color: #e94560;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    
    &:hover {
      background-color: #d63553;
    }
  }
}

.projects-table-container {
  overflow-x: auto;
}

.projects-table {
  width: 100%;
  border-collapse: collapse;

  tbody {
    tr {
      cursor: pointer;
      transition: background-color 0.15s ease-in-out;

      &:hover {
        background-color: #f9fafb; /* Or a subtle color */
      }
    }
  }
  
  th, td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #e5e7eb;
  }
  
  th {
    font-weight: 500;
    color: #374151;
    background-color: #f9fafb;
  }
  
  .project-name-link {
    color: #e94560;
    text-decoration: none;
    font-weight: 500;
    
    &:hover {
      text-decoration: underline;
    }
  }
  
  .actions {
    display: flex;
    gap: 8px;
  }
  
  .action-button {
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    border: 1px solid #e5e7eb;
    background-color: white;
    
    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
    
    &.edit {
      color: #059669;
      
      &:hover:not(:disabled) {
        background-color: #f0fdf4;
      }
    }
    
    &.delete {
      color: #dc2626;
      
      &:hover:not(:disabled) {
        background-color: #fef2f2;
      }
    }
  }
}

.loading-state {
  text-align: center;
  padding: 20px;
  color: #6b7280;
}

.empty-state {
  text-align: center;
  padding: 40px;
  color: #6b7280;
  background-color: #f9fafb;
  border-radius: 6px;
  border: 1px dashed #e5e7eb;
}

.pagination {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
  margin-top: 24px;
  
  .page-button {
    padding: 8px 16px;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    background-color: white;
    color: #374151;
    cursor: pointer;
    
    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
    
    &:hover:not(:disabled) {
      background-color: #f9fafb;
    }
  }
  
  .page-info {
    color: #6b7280;
  }
}
</style>
