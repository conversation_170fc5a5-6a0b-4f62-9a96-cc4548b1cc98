import { Queue, Worker, Job } from 'bullmq';
import Redis from 'ioredis';
import { config } from 'dotenv';
import { TestRunnerService } from './test-runner';

// Load environment variables
config();

// Redis connection configuration for BullMQ
const redisConfig = {
  host: process.env.REDIS_HOST || '127.0.0.1',
  port: parseInt(process.env.REDIS_PORT || '6379'),
  password: process.env.REDIS_PASSWORD,
  maxRetriesPerRequest: null, // Required by BullMQ
  retryDelayOnFailover: 1000,
  enableReadyCheck: false,
  lazyConnect: true,
  connectTimeout: 10000,
  db: parseInt(process.env.REDIS_DB || '3'), // Use DB 3 for TestRun
};

// Debug Redis configuration
console.log('🔧 Redis Config Debug:', {
  host: redisConfig.host,
  port: redisConfig.port,
  password: redisConfig.password ? '***' : 'undefined',
  db: redisConfig.db
});

// Create Redis connection with error handling
const connection = new Redis(redisConfig);

// Create a separate connection for the worker to avoid conflicts
const workerConnection = new Redis(redisConfig);

// Enhanced Redis error handling
connection.on('error', (err) => {
  if (err.message.includes('NOAUTH')) {
    console.error('❌ Redis Authentication Error: Please check REDIS_PASSWORD in .env file');
    console.log('💡 Current Redis config:', {
      host: redisConfig.host,
      port: redisConfig.port,
      password: redisConfig.password ? '***' : 'undefined',
      db: redisConfig.db
    });
  } else {
    console.error('❌ Redis connection error:', err.message);
  }
});

connection.on('connect', () => {
  console.log('✅ Connected to Redis successfully');
});

connection.on('ready', () => {
  console.log('✅ Redis is ready to accept commands');
});

// Test execution job data interface for test runs
interface TestJobData {
  apiKey: string;
  testData: {
    testCaseId: string;
    tcId: string;
    steps: any[];
    testCase: any;
    authToken?: string;
    projectId?: string;
    testRunId?: string;
  };
  clientId: string;
  timestamp: number;
}

export class QueueService {
  private static testQueue: Queue<TestJobData>;
  private static worker: Worker<TestJobData>;
  private static isInitialized = false;

  // Initialize the queue system
  static async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      // Create the test execution queue with concurrency of 1
      this.testQueue = new Queue<TestJobData>('test-execution', {
        connection: connection, // Use connection instance
        defaultJobOptions: {
          removeOnComplete: 10, // Keep last 10 completed jobs
          removeOnFail: 20,     // Keep last 20 failed jobs
          attempts: 3,          // Retry failed jobs up to 3 times
          backoff: {
            type: 'exponential',
            delay: 2000,
          },
        },
      });

      // Create worker with concurrency of 1 (only 1 job at a time)
      this.worker = new Worker<TestJobData>(
        'test-execution',
        async (job: Job<TestJobData>) => {
          return await this.processTestJob(job);
        },
        {
          connection: connection, // Use same connection instance
          concurrency: 1, // Only process 1 job at a time
        }
      );

      // Worker event handlers
      this.worker.on('completed', async (job) => {
        console.log(`✅ Test job ${job.id} completed for API key: ${job.data.apiKey}`);
        await this.notifyJobStatus(job.data.clientId, 'completed', job.data);
      });

      this.worker.on('failed', async (job, err) => {
        console.error(`❌ Test job ${job?.id} failed for API key: ${job?.data.apiKey}`, err);
        if (job) {
          await this.notifyJobStatus(job.data.clientId, 'failed', job.data, err.message);
        }
      });

      this.worker.on('active', async (job) => {
        console.log(`🔄 Test job ${job.id} started for API key: ${job.data.apiKey}`);
        await this.notifyJobStatus(job.data.clientId, 'active', job.data);
      });

      // Add worker error and stalled event handlers for debugging
      this.worker.on('error', (err) => {
        console.error('🚨 Worker error:', err);
      });

      this.worker.on('stalled', (jobId) => {
        console.warn(`⚠️ Job ${jobId} stalled`);
      });

      this.worker.on('ready', () => {
        console.log('🟢 Worker is ready to process jobs');
      });

      // Add more debugging events
      this.worker.on('drained', () => {
        console.log('🏁 Worker drained - no more jobs to process');
      });

      this.worker.on('paused', () => {
        console.log('⏸️ Worker paused');
      });

      this.worker.on('resumed', () => {
        console.log('▶️ Worker resumed');
      });

      // Queue event handlers - suppress error logging
      this.testQueue.on('error', () => {
        // Silently ignore queue errors - they don't affect core functionality
      });

      console.log('🚀 BullMQ Queue Service initialized successfully');
      this.isInitialized = true;

    } catch (error) {
      console.error('Failed to initialize Queue Service:', error);
      throw error;
    }
  }

  // Add a test job to the queue
  static async addTestJob(
    apiKey: string,
    testData: any,
    clientId: string
  ): Promise<{ jobId: string; position: number }> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    try {
      const jobData: TestJobData = {
        apiKey,
        testData,
        clientId,
        timestamp: Date.now(),
      };

      // Get queue stats to determine if we should add a small delay
      const activeBefore = await this.testQueue.getActive();
      const waitingBefore = await this.testQueue.getWaiting();
      const shouldWait = activeBefore.length > 0 || waitingBefore.length > 0;

      const job = await this.testQueue.add(
        `test-run-${apiKey}-${Date.now()}`,
        jobData,
        {
          delay: shouldWait ? 100 : 0, // Small delay if there are jobs ahead to ensure proper status
        }
      );

      // Debug: Check if job was actually added to the queue
      console.log(`🔍 Test run job ${job.id} added with status:`, await job.getState());

      // Force check queue status after adding job
      setTimeout(async () => {
        const waiting = await this.testQueue.getWaiting();
        const active = await this.testQueue.getActive();
        console.log(`🔍 Queue check after adding test run job ${job.id}: ${waiting.length} waiting, ${active.length} active`);
        if (waiting.length > 0) {
          console.log(`🔍 Waiting test run jobs:`, waiting.map(j => j.id));
        }
      }, 500);

      // Calculate position: the job was added after existing jobs
      const totalJobsAhead = activeBefore.length + waitingBefore.length;
      const position = totalJobsAhead + 1; // Position in the overall queue

      console.log(`📝 Test run job ${job.id} added to queue for API key: ${apiKey}, position: ${position}`);
      console.log(`📊 Queue status: ${activeBefore.length} active, ${waitingBefore.length} waiting`);

      // Notify client about job being queued
      if (totalJobsAhead > 0) {
        // Job is waiting because there are other jobs ahead
        await this.notifyJobStatus(clientId, 'waiting', jobData);
        console.log(`⏳ Test run job ${job.id} is waiting in queue at position ${position} (${activeBefore.length} active, ${waitingBefore.length} waiting ahead)`);
      } else {
        // Job will start immediately
        console.log(`🚀 Test run job ${job.id} will start immediately`);
      }

      return {
        jobId: job.id!,
        position: position,
      };

    } catch (error) {
      console.error('Failed to add test run job to queue:', error);
      throw error;
    }
  }

  // Process a test job
  private static async processTestJob(job: Job<TestJobData>): Promise<void> {
    const { apiKey, testData, clientId } = job.data;

    try {
      console.log(`🔄 Processing test run job ${job.id} for API key: ${apiKey}, client: ${clientId}`);

      // Add a small delay to make queue processing visible
      await new Promise(resolve => setTimeout(resolve, 1000)); // 1 second delay

      // Update job progress
      await job.updateProgress(10);

      // Don't check for client connection - let the test runner handle it
      // The queue should process jobs regardless of WebSocket connection state
      console.log(`🚀 Executing test run job ${job.id} for API key: ${apiKey}, client: ${clientId} (connection-independent)`);

      await job.updateProgress(25);

      // Execute the test using the existing TestRunnerService with clientId for message filtering
      await TestRunnerService.executeTest(apiKey, testData, clientId);

      await job.updateProgress(100);

      console.log(`✅ Test run job ${job.id} completed successfully for API key: ${apiKey}, client: ${clientId || 'unknown'}`);

    } catch (error) {
      console.error(`❌ Test run job ${job.id} execution failed for API key: ${apiKey}, client: ${clientId || 'unknown'}:`, error);
      throw error; // Re-throw to mark job as failed
    }
  }

  // Notify client about job status changes
  private static async notifyJobStatus(
    clientId: string,
    status: 'waiting' | 'active' | 'completed' | 'failed',
    jobData: TestJobData,
    error?: string
  ): Promise<void> {
    try {
      // Use clientId to get the specific WebSocket connection
      const ws = TestRunnerService.getClientConnection(clientId);
      if (ws && ws.readyState === 1) { // WebSocket.OPEN
        console.log(`📤 Notifying client ${clientId} about job status: ${status}`);

        // Get current queue stats
        const queueStats = await this.getQueueStats();

        ws.send(JSON.stringify({
          type: 'queue_status',
          status,
          jobId: clientId,
          apiKey: jobData.apiKey,
          timestamp: Date.now(),
          error: error || undefined,
          queueStats: queueStats,
        }));
      } else {
        console.log(`⚠️ No active WebSocket connection found for client ${clientId}`);
      }
    } catch (err) {
      console.error('Failed to notify client about job status:', err);
    }
  }

  // Get queue statistics
  static async getQueueStats(): Promise<any> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    try {
      const waiting = await this.testQueue.getWaiting();
      const active = await this.testQueue.getActive();
      const completed = await this.testQueue.getCompleted();
      const failed = await this.testQueue.getFailed();

      return {
        waiting: waiting.length,
        active: active.length,
        completed: completed.length,
        failed: failed.length,
        total: waiting.length + active.length,
      };
    } catch (error) {
      console.error('Failed to get queue stats:', error);
      return { waiting: 0, active: 0, completed: 0, failed: 0, total: 0 };
    }
  }

  // Get job position in queue
  static async getJobPosition(jobId: string): Promise<number> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    try {
      const waiting = await this.testQueue.getWaiting();
      const position = waiting.findIndex(job => job.id === jobId) + 1;
      return position || 0;
    } catch (error) {
      console.error('Failed to get job position:', error);
      return 0;
    }
  }

  // Clean up completed and failed jobs
  static async cleanQueue(): Promise<void> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    try {
      await this.testQueue.clean(24 * 60 * 60 * 1000, 10, 'completed'); // Keep completed jobs for 24 hours
      await this.testQueue.clean(7 * 24 * 60 * 60 * 1000, 20, 'failed'); // Keep failed jobs for 7 days
      console.log('🧹 Queue cleaned successfully (TestRun)');
    } catch (error) {
      console.error('Failed to clean queue:', error);
    }
  }

  // Get the queue instance for Bull Board
  static getQueue(): Queue<TestJobData> {
    return this.testQueue;
  }

  // Debug method to check worker status
  static async debugWorkerStatus(): Promise<void> {
    if (!this.isInitialized) {
      console.log('❌ Queue service not initialized (TestRun)');
      return;
    }

    try {
      console.log('🔍 Worker Status Debug (TestRun):');
      console.log('- Worker exists:', !!this.worker);
      console.log('- Worker is running:', this.worker?.isRunning());
      console.log('- Worker is paused:', this.worker?.isPaused());

      const waiting = await this.testQueue.getWaiting();
      const active = await this.testQueue.getActive();
      const completed = await this.testQueue.getCompleted();
      const failed = await this.testQueue.getFailed();

      console.log('🔍 Queue Status (TestRun):');
      console.log(`- Waiting: ${waiting.length}`);
      console.log(`- Active: ${active.length}`);
      console.log(`- Completed: ${completed.length}`);
      console.log(`- Failed: ${failed.length}`);

      if (waiting.length > 0) {
        console.log('🔍 Waiting job IDs:', waiting.map(j => j.id));
      }
    } catch (error) {
      console.error('❌ Error checking worker status (TestRun):', error);
    }
  }

  // Graceful shutdown
  static async shutdown(): Promise<void> {
    if (!this.isInitialized) {
      return;
    }

    try {
      console.log('🔄 Shutting down Queue Service (TestRun)...');

      if (this.worker) {
        await this.worker.close();
      }

      if (this.testQueue) {
        await this.testQueue.close();
      }

      await connection.quit();

      console.log('✅ Queue Service shut down successfully (TestRun)');
      this.isInitialized = false;
    } catch (error) {
      console.error('Error during Queue Service shutdown (TestRun):', error);
    }
  }
}
