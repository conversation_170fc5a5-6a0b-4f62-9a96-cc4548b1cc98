import { WebSocket } from 'ws';
import { exec, ChildProcess } from 'child_process';
import * as fs from 'fs';
import * as path from 'path';
import axios from 'axios';


function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

function formatTestOutput(output: string): string {
  // Remove all ANSI escape sequences and control characters
  output = output
    // Remove ANSI color codes (e.g., \x1B[38;5;45;1m, \x1B[0m)
    .replace(/\x1B\[[0-9;]*m/g, '')
    // Remove cursor movement sequences (e.g., [1A[2K)
    .replace(/\[1A\[2K/g, '')
    // Remove other ANSI escape sequences
    .replace(/\x1B\[[0-9;]*[A-Za-z]/g, '')
    // Remove control characters
    .replace(/[\x00-\x1F\x7F]/g, '')
    // Clean up extra whitespace
    .trim();

  // Skip empty lines after cleaning
  if (!output) {
    return '';
  }

  // Skip debug/verbose logs that aren't useful for users
  if (output.includes('pw:api') ||
      output.includes('To open last HTML report run:') ||
      output.includes('npx playwright show-report') ||
      output.includes('Default Test Name') ||
      output.includes('fonts loaded') ||
      output.includes('waiting for fonts to load') ||
      output.includes('taking page screenshot') ||
      output.includes('navigations have finished') ||
      output.includes('waiting for scheduled navigations') ||
      output.includes('done scrolling') ||
      output.includes('scrolling into view') ||
      output.includes('performing click action') ||
      output.includes('click action done') ||
      output.includes('attempting') ||
      output.includes('waiting for element to be') ||
      output.includes('element is visible, enabled') ||
      output.includes('locator resolved to') ||
      output.includes('Authenticating with:') ||
      output.includes('auth/login') ||
      output.includes('Recording completion_input') ||
      output.includes('Recording completion_output') ||
      output.includes('Successfully recorded') ||
      output.includes('Connected to WebSocket server') ||
      output.includes('Connecting to WebSocket server') ||
      output.includes('Test completed successfully') ||
      output.includes('waiting for locator')) {
    return '';
  }

  // Format test steps with emojis
  if (output.includes('Running')) {
    return `🚀 ${output}`;
  }

  if (output.includes('Step: prompt')) {
    return `🌐 ${output}`;
  }

  if (output.includes('Step: goto')) {
    return `🌐 ${output}`;
  }

  if (output.includes('Step: navigate')) {
    return `🌐 ${output}`;
  }

  if (output.includes('Step: write') || output.includes('Step: fill')) {
    return `⌨️ ${output}`;
  }

  if (output.includes('Step: click')) {
    return `🖱️ ${output}`;
  }

  if (output.includes('Step: assertText')) {
    return `🔎 ${output}`;
  }

  if (output.includes('goto')) {
    return `🌐 ${output}`;
  }

  if (output.includes('visit')) {
    return `🌐 ${output}`;
  }

  if (output.includes('click')) {
    return `🖱️ ${output}`;
  }

  if (output.includes('fill')) {
    return `⌨️ ${output}`;
  }

  if (output.includes('expect(')) {
    return `🔎 ${output}`;
  }

  // Format test results
  if (output.includes('passed') || output.includes('✓')) {
    return `✅ ${output}`;
  }

  if (output.includes('failed') || output.includes('✘')) {
    return `❌ ${output}`;
  }

  if (output.includes('Error:')) {
    return `⚠️ ${output}`;
  }

  if (output.includes('Authenticating with:')) {
    return `🔐 ${output}`;
  }

  if (output.includes('navigated to')) {
    return `🌐 ${output}`;
  }

  // Return cleaned output
  return output;
}

export class TestRunnerService {
  private static activeClients = new Map<string, WebSocket>();
  private static activeProcesses = new Map<string, ChildProcess>();
  private static readonly CONNECTION_TIMEOUT = 60000; // 60 seconds timeout
  private static sentLogMessages = new Set<string>();
  private static activeTests = new Map<string, boolean>();
  private static clientHeartbeats = new Map<string, number>();
  private static readonly HEARTBEAT_INTERVAL = 30000; // 30 seconds
  private static readonly HEARTBEAT_TIMEOUT = 90000; // 90 seconds
  private static lastTestRunTimes = new Map<string, number>();
  private static testLogs = new Map<string, string[]>();

  // Start the heartbeat checker
  static startHeartbeatChecker() {
    setInterval(() => {
      const now = Date.now();
      
      // Check each client's last heartbeat
      this.clientHeartbeats.forEach((lastHeartbeat, apiKey) => {
        if (now - lastHeartbeat > this.HEARTBEAT_TIMEOUT) {
          console.log(`Client ${apiKey} heartbeat timeout, removing client`);
          this.removeClient(apiKey);
        }
      });
    }, this.HEARTBEAT_INTERVAL);
  }

  static addClient(apiKey: string, ws: WebSocket) {
    // Check if there's already a client with this API key
    const existingClient = this.activeClients.get(apiKey);
    if (existingClient && existingClient.readyState === WebSocket.OPEN) {
      console.log(`Client with API key ${apiKey} already exists. Closing previous connection.`);
      existingClient.close(1000, "New connection with same API key");
      // Don't kill the process, just update the client reference
    }

    // Set connection timeout
    const connectionTimeout = setTimeout(() => {
      if (ws.readyState !== WebSocket.OPEN) {
        ws.terminate();
        this.removeClient(apiKey);
        console.log(`Connection timeout for client ${apiKey}`);
      }
    }, this.CONNECTION_TIMEOUT);

    // Clear timeout when connection is established
    ws.once('open', () => {
      clearTimeout(connectionTimeout);
      console.log(`Client ${apiKey} connected successfully`);
    });

    // Set initial heartbeat
    this.clientHeartbeats.set(apiKey, Date.now());

    this.activeClients.set(apiKey, ws);
    
    // Set up ping-pong to keep connection alive
    const pingInterval = setInterval(() => {
      if (ws.readyState === WebSocket.OPEN) {
        ws.ping();
      } else {
        clearInterval(pingInterval);
        this.removeClient(apiKey);
      }
    }, 15000);

    // Handle pong responses
    ws.on('pong', () => {
      // Update heartbeat time
      this.clientHeartbeats.set(apiKey, Date.now());
    });

    // Handle ping messages from client
    ws.on('message', (message) => {
      try {
        const data = JSON.parse(message.toString());
        if (data.type === 'ping') {
          // Update heartbeat time
          this.clientHeartbeats.set(apiKey, Date.now());
          
          // Send pong response
          if (ws.readyState === WebSocket.OPEN) {
            ws.send(JSON.stringify({ type: 'pong' }));
          }
        }
      } catch (error) {
        // Not a JSON message or not a ping, ignore
      }
    });

    ws.on('error', (error) => {
      console.error(`WebSocket error for client ${apiKey}:`, error);
      clearInterval(pingInterval);
      this.removeClient(apiKey);
    });

    ws.on('close', () => {
      console.log(`Client ${apiKey} disconnected`);
      clearInterval(pingInterval);
      this.removeClient(apiKey);
      
      // Only kill the process if there are no other active clients with this API key
      if (!this.activeClients.has(apiKey)) {
        const process = this.activeProcesses.get(apiKey);
        if (process) {
          process.kill();
          this.activeProcesses.delete(apiKey);
        }
      }
    });
  }

  static removeClient(apiKey: string) {
    const ws = this.activeClients.get(apiKey);
    if (ws) {
      ws.terminate();
      this.activeClients.delete(apiKey);
      console.log(`Removed client ${apiKey}`);
    }
  }

  // Get client connection by ID (used by queue service)
  static getClientConnection(clientId: string): WebSocket | undefined {
    return this.activeClients.get(clientId);
  }

  static async executeTest(apiKey: string, testData: any, clientId?: string): Promise<void> {
    // Use clientId if provided, otherwise fall back to apiKey for backward compatibility
    const connectionId = clientId || apiKey;
    let ws = this.activeClients.get(connectionId);

    // Always run the test, regardless of WebSocket connection state
    if (!ws || ws.readyState !== WebSocket.OPEN) {
      console.log(`⚠️ No active WebSocket connection for ${connectionId}, running test in background mode`);
      ws = undefined; // Ensure we handle the undefined case properly
    } else {
      console.log(`✅ Active WebSocket connection found for ${connectionId}, running test with real-time updates`);
    }

    // Try to validate user JWT token with backend, fall back to JWT_SECRET if not provided
    const userJwtToken = testData.userJwtToken;
    let authToken: string;

    if (userJwtToken) {
      console.log('Validating user JWT token with backend...');
      const isAuthenticated = await this.validateUserToken(userJwtToken);
      if (isAuthenticated) {
        console.log('User authentication successful, using user JWT token');
        authToken = userJwtToken;
      } else {
        console.log('User JWT token validation failed, falling back to JWT_SECRET');
        authToken = this.generateServiceToken();
      }
    } else {
      console.log('No user JWT token provided, using JWT_SECRET fallback');
      authToken = this.generateServiceToken();
    }

    // Check if there's a recent test run that needs cleanup time
    const lastRunTime = this.lastTestRunTimes.get(apiKey);
    const now = Date.now();
    if (lastRunTime && (now - lastRunTime < 5000)) {
      // If less than 5 seconds since last test, wait a bit
      const waitTime = 5000 - (now - lastRunTime);
      console.log(`Recent test detected, waiting ${waitTime}ms for cleanup...`);
      await sleep(waitTime);
    }

    try {
      // Record this test run time and start time for duration calculation
      const startTime = Date.now();
      this.lastTestRunTimes.set(apiKey, startTime);

      // Add start time to test data for duration calculation
      testData.startTime = startTime;

      // Initialize logs collection for this test
      this.testLogs.set(apiKey, []);

      // Send test start notification
      if (ws && ws.readyState === WebSocket.OPEN) {
        ws.send(JSON.stringify({
          type: 'test_start',
          testCaseId: testData.testCaseId,
          tcId: testData.tcId
        }));
      }

      // Use existing master.spec.ts file instead of generating a new one
      const testFilePath = path.join(process.cwd(), 'tests', 'master.spec.ts');
      console.log(`Test data for: ${testData.testCase.title}`);
      console.log(`Using existing test file: ${testFilePath}`);

      // Generate JWT token for AgentQ library authentication
      const agentqJwtToken = authToken; // Use the same token we validated earlier

      // Set environment variables for the test
      const env = {
        ...process.env,
        TEST_DATA: JSON.stringify(testData),
        AGENTQ_API_KEY: apiKey,
        AGENTQ_TOKEN: apiKey, // AgentQ library looks for this variable
        AGENTQ_JWT_TOKEN: agentqJwtToken, // Pass JWT token to AgentQ library
        NODE_ENV: 'production', // Tell AgentQ library to use VITE_WEBSOCKET_URL
        VITE_WEBSOCKET_URL: `${process.env.VITE_WEBSOCKET_URL}`, // Use TestRun WebSocket server
        BACKEND_URL: process.env.AGENTQ_API_URL || 'http://localhost:3010' // Backend URL for test
      };

      const command = `npx playwright test "${testFilePath}" --reporter=line`;
      console.log(`Executing: ${command}`);

      // Store detailed error information
      let detailedError = '';

      const childProcess = exec(command, { env }, async (error, stdout, stderr) => {
        // Clean up process reference
        this.activeProcesses.delete(apiKey);

        // Get collected logs
        const logs = this.testLogs.get(apiKey) || [];
        const status = error ? 'failed' : 'passed';
        
        // If there was an error, capture the detailed error message
        if (error && stderr) {
          detailedError = stderr;
          // Add the detailed error to logs if it's not already there
          if (!logs.some(log => log.includes(stderr.substring(0, 100)))) {
            logs.push(`⚠️ ${stderr}`);
          }
        }

        // Save test results to backend using authenticated token
        try {
          await this.saveTestResults(testData, logs, status, authToken, detailedError);
        } catch (saveError) {
          console.error('Failed to save test results:', saveError);
        }

        // Upload video if test completed (regardless of pass/fail)
        try {
          await this.uploadTestVideo(testData, authToken);
        } catch (videoError) {
          console.error('Failed to upload test video:', videoError);
        }

        // Upload screenshot if test completed (regardless of pass/fail)
        try {
          await this.uploadTestScreenshot(testData, authToken);
        } catch (screenshotError) {
          console.error('Failed to upload test screenshot:', screenshotError);
        }

        // Clean up logs from memory
        this.testLogs.delete(apiKey);

        if (error) {
          console.error('Test execution failed:', error);
          if (ws && ws.readyState === WebSocket.OPEN) {
            ws.send(JSON.stringify({
              type: 'test_complete',
              status: 'failed',
              error: error.message,
              stdout,
              stderr
            }));
          }
        } else {
          console.log('Test execution completed successfully');
          if (ws && ws.readyState === WebSocket.OPEN) {
            ws.send(JSON.stringify({
              type: 'test_complete',
              status: 'passed',
              stdout,
              stderr
            }));
          }
        }

        // Set the test as inactive
        this.activeTests.set(apiKey, false);

        // Close the connection after sending the completion message
        console.log(`Test execution completed for client ${apiKey}, closing connection`);

        // Add a small delay before closing to ensure the message is sent
        setTimeout(() => {
          if (ws && ws.readyState === WebSocket.OPEN) {
            console.log(`Closing connection for client ${apiKey}`);
            ws.close(1000, "Test execution completed");
          }
        }, 1000);
      });

      // Set the test as active
      this.activeTests.set(apiKey, true);

      // Store the process reference
      this.activeProcesses.set(apiKey, childProcess);

      // Stream output in real-time
      childProcess.stdout?.on('data', (data) => {
        const output = data.toString();
        const formattedOutput = formatTestOutput(output);

        if (formattedOutput) {
          // Check if this exact message was already sent
          if (!this.sentLogMessages.has(formattedOutput)) {
            console.log('Test output:', formattedOutput);

            // Add to logs collection
            const logs = this.testLogs.get(apiKey) || [];
            logs.push(formattedOutput);
            this.testLogs.set(apiKey, logs);

            if (ws && ws.readyState === WebSocket.OPEN) {
              ws.send(JSON.stringify({
                type: 'test_output',
                output: formattedOutput
              }));
            }

            // Add to the set of sent messages
            this.sentLogMessages.add(formattedOutput);
          }
        }
      });

      childProcess.stderr?.on('data', (data) => {
        const output = data.toString();
        const formattedOutput = formatTestOutput(output);

        if (formattedOutput) {
          // Check if this exact message was already sent
          if (!this.sentLogMessages.has(formattedOutput)) {
            console.log('Test error:', formattedOutput);

            // Add to logs collection
            const logs = this.testLogs.get(apiKey) || [];
            logs.push(`ERROR: ${formattedOutput}`);
            this.testLogs.set(apiKey, logs);

            if (ws && ws.readyState === WebSocket.OPEN) {
              ws.send(JSON.stringify({
                type: 'test_output',
                output: formattedOutput,
                isError: true
              }));
            }

            // Add to the set of sent messages
            this.sentLogMessages.add(formattedOutput);
          }
        }
      });

      // Clear the sent messages set when the test completes
      childProcess.on('close', async (code) => {
        this.sentLogMessages.clear();
        this.activeTests.set(apiKey, false);

        // Note: saveTestResults is already called in the exec callback above
        // No need to call it again here to avoid duplicate entries

        // Only send completion message if the WebSocket is still open
        if (ws && ws.readyState === WebSocket.OPEN) {
          if (code !== 0) {
            ws.send(JSON.stringify({
              type: 'test_complete',
              status: 'failed',
              message: `Test execution failed with code ${code}`
            }));
          } else {
            ws.send(JSON.stringify({
              type: 'test_complete',
              status: 'passed',
              message: 'Test execution completed successfully'
            }));
          }

          // Close the connection after sending the completion message
          setTimeout(() => {
            if (ws && ws.readyState === WebSocket.OPEN) {
              console.log(`Closing connection for client ${apiKey} after test completion`);
              ws.close(1000, "Test execution completed");
            }
          }, 1000);
        }
      });

    } catch (error) {
      console.error('Error in executeTest:', error);
      if (ws && ws.readyState === WebSocket.OPEN) {
        ws.send(JSON.stringify({
          type: 'test_error',
          message: error instanceof Error ? error.message : 'Failed to execute test'
        }));
      }
    }
  }



  static handleClientDisconnect(apiKey: string): void {
    // Check if there's an active test for this client
    if (this.activeTests.get(apiKey)) {
      console.log(`Client ${apiKey} disconnected but test is still running. Allowing test to complete.`);
      // Don't kill the process, let it complete
    } else {
      // If no active test or test already completed, clean up
      const process = this.activeProcesses.get(apiKey);
      if (process) {
        console.log(`Cleaning up process for disconnected client ${apiKey}`);
        process.kill();
        this.activeProcesses.delete(apiKey);
      }
    }
    
    // Make sure to clean up any other resources
    this.sentLogMessages.clear();
    this.activeTests.set(apiKey, false);
  }

  static canRunTest(apiKey: string): boolean {
    // Check if there's already an active test for this client
    if (this.activeTests.get(apiKey)) {
      return false;
    }

    // Check if there's a process still running for this client
    if (this.activeProcesses.has(apiKey)) {
      return false;
    }

    return true;
  }

  // Generate service JWT token using JWT_SECRET
  private static generateServiceToken(): string {
    const jwtSecret = process.env.JWT_SECRET || 'your_jwt_secret_key_change_in_production';
    const jwt = require('jsonwebtoken');
    return jwt.sign(
      {
        sub: 'test-runner-service',
        role: 'service'
      },
      jwtSecret,
      { expiresIn: '1h' }
    );
  }

  // Validate user JWT token with backend
  private static async validateUserToken(userJwtToken: string): Promise<boolean> {
    try {
      const backendUrl = process.env.AGENTQ_API_URL || 'http://localhost:3010';

      // Try to validate the token by calling the profile endpoint
      const response = await axios.get(`${backendUrl}/auth/profile`, {
        headers: {
          'Authorization': `Bearer ${userJwtToken}`
        },
        timeout: 5000
      });

      if (response.status === 200 && response.data) {
        console.log('User JWT token validation successful:', response.data.email || 'Unknown user');
        return true;
      }

      return false;
    } catch (error) {
      console.error('User JWT token validation failed:', error);

      // If profile endpoint doesn't exist, try login verification endpoint
      try {
        const backendUrl = process.env.AGENTQ_API_URL || 'http://localhost:3010';
        const verifyResponse = await axios.post(`${backendUrl}/auth/verify`, {}, {
          headers: {
            'Authorization': `Bearer ${userJwtToken}`
          },
          timeout: 5000
        });

        if (verifyResponse.status === 200) {
          console.log('User JWT token verification successful');
          return true;
        }
      } catch (verifyError) {
        console.error('User JWT token verification also failed:', verifyError);
      }

      return false;
    }
  }

  // Save test results to backend API
  private static async saveTestResults(
    testData: any,
    logs: string[],
    status: string,
    authToken: string,
    detailedError: string = ''
  ): Promise<void> {
    try {
      const backendUrl = process.env.AGENTQ_API_URL || 'http://localhost:3010';

      // Use the provided auth token for backend authentication
      console.log('Using auth token for backend authentication');
      const token = authToken;

      // Helper function to validate UUID format
      const isValidUUID = (uuid: string): boolean => {
        const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
        return uuidRegex.test(uuid);
      };

      // Extract test duration from logs
      let duration: number | null = null;

      // Try multiple patterns to extract duration
      for (const log of logs) {
        // Pattern 1: "✅ 1 passed (3.3s)" or "❌ 1 failed (2.1s)"
        let durationMatch = log.match(/(?:passed|completed|finished|failed|took)\s*\(?([\d.]+)s\)?/i);

        // Pattern 2: Playwright format like "1 passed (5.2s)"
        if (!durationMatch) {
          durationMatch = log.match(/\d+\s+(?:passed|failed)\s+\(([\d.]+)s\)/i);
        }

        // Pattern 3: Look for any duration in seconds format
        if (!durationMatch) {
          durationMatch = log.match(/\(([\d.]+)s\)/);
        }

        // Pattern 4: Look for "in X.Xs" format
        if (!durationMatch) {
          durationMatch = log.match(/in\s+([\d.]+)s/i);
        }

        if (durationMatch && durationMatch[1]) {
          // Convert to milliseconds and store
          duration = Math.round(parseFloat(durationMatch[1]) * 1000);
          console.log(`Extracted test duration: ${duration}ms from log: "${log}"`);
          break;
        }
      }

      // If no duration found in logs, calculate based on execution time
      if (!duration && testData.startTime) {
        const endTime = Date.now();
        duration = endTime - testData.startTime;
        console.log(`Calculated test duration: ${duration}ms based on execution time`);
      }

      // If still no duration, provide a reasonable default based on log count
      if (!duration) {
        // Estimate duration based on number of logs (rough approximation)
        duration = Math.max(1000, logs.length * 500); // At least 1 second, ~500ms per log entry
        console.log(`Estimated test duration: ${duration}ms based on ${logs.length} log entries`);
      }

      // Debug: Log the received test data structure
      console.log('Received testData structure:', {
        testCaseId: testData.testCaseId,
        tcId: testData.tcId,
        projectId: testData.projectId,
        testCaseProjectId: testData.testCase?.projectId,
        hasTestCase: !!testData.testCase,
        testCaseKeys: testData.testCase ? Object.keys(testData.testCase) : [],
        duration: duration
      });

      // Ensure we have valid UUIDs
      const testCaseId = testData.testCaseId && isValidUUID(testData.testCaseId)
        ? testData.testCaseId
        : "00000000-0000-0000-0000-000000000001";

      // Try to get projectId from multiple sources
      let projectId = "00000000-0000-0000-0000-000000000000";

      // First check if projectId is directly available
      if (testData.projectId && isValidUUID(testData.projectId)) {
        projectId = testData.projectId;
      } 
      // Then check if it's in the testCase object
      else if (testData.testCase?.projectId && isValidUUID(testData.testCase.projectId)) {
        projectId = testData.testCase.projectId;
      }
      // If still not found, try to fetch it from the API using the testCaseId
      else if (testCaseId && testCaseId !== "00000000-0000-0000-0000-000000000001") {
        try {
          // Make a request to get the test case details
          const response = await axios.get(`${backendUrl}/test-cases/${testCaseId}`, {
            headers: {
              'Authorization': `Bearer ${token}`
            },
            timeout: 5000
          });
          
          if (response.data && response.data.projectId && isValidUUID(response.data.projectId)) {
            projectId = response.data.projectId;
            console.log(`Retrieved projectId ${projectId} from API for testCaseId ${testCaseId}`);
          }
        } catch (error) {
          console.error(`Failed to fetch projectId for testCaseId ${testCaseId}:`, error);
        }
      }

      console.log('Using projectId:', projectId, 'from:', {
        direct: testData.projectId,
        fromTestCase: testData.testCase?.projectId,
        fromAPI: projectId !== testData.projectId && projectId !== testData.testCase?.projectId ? projectId : undefined
      });

      // Extract error message if test failed
      let errorMessage = null;
      if (status === 'failed') {
        // First use the detailed error if available
        if (detailedError) {
          // Function to strip ANSI color codes
          const stripAnsi = (str: string) => {
            return str.replace(/[\u001b\u009b][[()#;?]*(?:[0-9]{1,4}(?:;[0-9]{0,4})*)?[0-9A-ORZcf-nqry=><]/g, '');
          };
          
          // Clean the detailed error
          const cleanedError = stripAnsi(detailedError);
          
          // Look for the most important parts of the error using regex
          const timedOutMatch = cleanedError.match(/Timed out .+expect\(.+\)\.toHaveText\(.+\)([\s\S]*?)Expected string: "([^"]+)"([\s\S]*?)Received string: "([^"]+)"/);
          if (timedOutMatch) {
            errorMessage = `Test failed: Timed out waiting for element to have text.\nExpected: "${timedOutMatch[2]}"\nActual: "${timedOutMatch[4]}"`;
          } else {
            // If no specific pattern match, extract the most useful lines
            const errorLines = cleanedError.split('\n').filter(line => 
              !line.includes('at /Users/') && 
              !line.includes('node:internal/') &&
              !line.includes('at Object.<anonymous>') &&
              !line.includes('at processTicksAndRejections') &&
              !line.includes('matcherResult:') &&
              !line.includes('Symbol(step)') &&
              !line.includes('stepId:') &&
              !line.includes('function:') &&
              !line.includes('steps:') &&
              !line.trim().startsWith('    at ') &&
              !line.trim().startsWith('{') &&
              !line.trim().startsWith('}') &&
              !line.trim().startsWith('[') &&
              !line.trim().startsWith(']') &&
              line.trim() !== ''
            );
            
            // Join the filtered lines to create a clean error message
            const filteredError = errorLines.join('\n').trim();
            
            // Extract just the core error message
            const coreErrorMatch = filteredError.match(/Error: (.*?)(?:Call log:|$)/s);
            if (coreErrorMatch) {
              errorMessage = coreErrorMatch[1].trim();
            } else {
              errorMessage = filteredError;
            }
          }
          
          // If the error message is too long, truncate it
          if (errorMessage && errorMessage.length > 2000) {
            errorMessage = errorMessage.substring(0, 1997) + '...';
          }
          
          console.log('Cleaned error message:', errorMessage);
        }
        
        // If no detailed error, look for error in logs (existing code)
        if (!errorMessage) {
          // First look for detailed error messages with "Timed out" or "ExpectError"
          for (const log of logs) {
            if ((log.includes('Timed out') && log.includes('expect(')) || 
                log.includes('ExpectError:') || 
                (log.includes('Error:') && log.includes('Expected') && log.includes('Received'))) {
              
              console.log('Found detailed error message in logs:', log);
              
              // Strip ANSI color codes
              const stripAnsi = (str: string) => {
                return str.replace(/[\u001b\u009b][[()#;?]*(?:[0-9]{1,4}(?:;[0-9]{0,4})*)?[0-9A-ORZcf-nqry=><]/g, '');
              };
              
              errorMessage = stripAnsi(log);
              break;
            }
          }
          
          // If still no detailed error, look for any error message
          if (!errorMessage) {
            for (const log of logs) {
              if (log.includes('Error:') || log.includes('❌')) {
                errorMessage = log;
                break;
              }
            }
          }
        }
      }

      // Create a more minimal payload with only essential fields
      const testResultData = {
        testCaseId: testCaseId,
        tcId: testData.tcId || "TC-UNKNOWN",
        projectId: projectId,
        status: status,
        logs: logs,
        duration: duration, // Include the extracted duration
        errorMessage: errorMessage, // Include error message if test failed
        // Send a simplified version of test data to reduce payload size
        testData: {
          title: testData.testCase?.title || "Unknown Test",
          steps: testData.steps?.length || 0
        },
        executedAt: new Date().toISOString(),
        summary: `Test ${status} with ${logs.length} log entries${duration ? ` in ${duration}ms` : ''}`
      };

      // For test runs, save to the test run results endpoint instead of temp-test-results
      let saveUrl = `${backendUrl}/temp-test-results`;

      // Check if this is a test run execution (has testRunId)
      if (testData.testRunId && testData.testRunId !== 'default-test-run') {
        // Save to test run results endpoint using the correct API route
        saveUrl = `${backendUrl}/projects/${projectId}/test-runs/${testData.testRunId}/test-results/tcId/${testData.tcId}`;
        console.log(`Saving test run results to ${saveUrl} with JWT auth`);

        // For test runs, we need to update the existing test result using PATCH method
        // Add a properly formatted execution time log for backend duration extraction
        const logsWithDuration = [...logs];
        if (duration) {
          logsWithDuration.push(`⏱️ Execution time: ${duration}ms`);
        }

        const patchData = {
          status: status,
          actualResult: errorMessage || `Test ${status}`,
          notes: `Automated test execution - ${status}\n\nExecution time: ${duration ? `${duration}ms` : 'N/A'}\n\nLogs:\n${logs.join('\n')}`,
          logs: logsWithDuration, // Include logs array with duration log for backend storage
          duration: duration,
          executionTime: duration // Also send as executionTime for compatibility
        };

        console.log('Sending PATCH data to backend:', JSON.stringify(patchData, null, 2));
        console.log('Duration being sent:', duration);
        console.log('Logs being sent:', logsWithDuration.slice(0, 3), '... (showing first 3 logs)');
        console.log('Last log (duration):', logsWithDuration[logsWithDuration.length - 1]);

        await axios.patch(saveUrl, patchData, {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          timeout: 10000, // 10 second timeout
        });

        console.log(`Test run results updated for test case: ${testData.testCaseId} (tcId: ${testData.tcId}) in test run: ${testData.testRunId}`);
      } else {
        // Fallback to temp-test-results for single test cases
        console.log(`Saving test results to ${saveUrl} with JWT auth`);

        await axios.post(saveUrl, testResultData, {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          timeout: 10000, // 10 second timeout
        });

        console.log(`Test results saved to backend for test case: ${testData.testCaseId}`);
      }
    } catch (error) {
      console.error('Failed to save test results to backend:', error);
      
      // Log more detailed error information
      if (axios.isAxiosError(error) && error.response) {
        console.error('Response status:', error.response.status);
        console.error('Response data:', error.response.data);
      }
    }
  }

  // Upload test video to backend
  private static async uploadTestVideo(testData: any, authToken: string): Promise<void> {
    try {
      const fs = require('fs');
      const path = require('path');
      const FormData = require('form-data');

      // Look for video files in test-results directory
      const testResultsDir = path.join(process.cwd(), 'test-results');

      if (!fs.existsSync(testResultsDir)) {
        console.log('No test-results directory found, skipping video upload');
        return;
      }

      // Find video files - look for patterns like master-{title}/video.webm
      const testTitle = testData.testCase?.title || 'Unknown';
      const sanitizedTitle = testTitle.replace(/[^a-zA-Z0-9\s-]/g, '').replace(/\s+/g, '-');

      // Try multiple possible video paths
      const possibleVideoPaths = [
        path.join(testResultsDir, `master-${sanitizedTitle}`, 'video.webm'),
        path.join(testResultsDir, `master-${testData.tcId}`, 'video.webm'),
        path.join(testResultsDir, `master-test`, 'video.webm')
      ];

      // Also search for any video.webm files in subdirectories
      const findVideoFiles = (dir: string): string[] => {
        const videoFiles: string[] = [];
        try {
          const items = fs.readdirSync(dir);
          for (const item of items) {
            const fullPath = path.join(dir, item);
            const stat = fs.statSync(fullPath);
            if (stat.isDirectory()) {
              videoFiles.push(...findVideoFiles(fullPath));
            } else if (item === 'video.webm') {
              videoFiles.push(fullPath);
            }
          }
        } catch (error) {
          console.error('Error reading directory:', error);
        }
        return videoFiles;
      };

      // Get all video files
      const allVideoFiles = findVideoFiles(testResultsDir);

      // Add the specific paths we're looking for
      const videoFiles = [...possibleVideoPaths.filter(p => fs.existsSync(p)), ...allVideoFiles];

      if (videoFiles.length === 0) {
        console.log('No video files found for upload');
        return;
      }

      // Use the most recent video file
      const videoFile = videoFiles.sort((a, b) => {
        const statA = fs.statSync(a);
        const statB = fs.statSync(b);
        return statB.mtime.getTime() - statA.mtime.getTime();
      })[0];

      console.log(`Found video file for upload: ${videoFile}`);

      // First, get the test result ID from the backend
      const backendUrl = process.env.AGENTQ_API_URL || 'http://localhost:3010';

      // Use the provided auth token for backend authentication
      console.log('Using auth token for video upload authentication');
      const token = authToken;

      let testResultId: string;
      let uploadUrl: string;

      // Always use test run endpoints for websocket_ai_test_run
      // For test runs, get the test result from the test run endpoint
      const projectId = testData.projectId || testData.testCase?.projectId;

      console.log(`Fetching test results from: ${backendUrl}/projects/${projectId}/test-runs/${testData.testRunId}/test-results`);
      console.log(`Using projectId: ${projectId}, testRunId: ${testData.testRunId}`);

      const testResultResponse = await axios.get(
        `${backendUrl}/projects/${projectId}/test-runs/${testData.testRunId}/test-results`,
        {
          headers: {
            'Authorization': `Bearer ${token}`
          },
          timeout: 5000,
          params: {
            limit: 50,
            sortField: 'createdAt',
            sortDirection: 'DESC'
          }
        }
      );

      // Handle different API response structures (results vs data)
      const testResults = testResultResponse.data.results || testResultResponse.data.data || testResultResponse.data;

      if (!testResults || testResults.length === 0) {
        console.log('No test results found in test run for video upload');
        console.log('API response structure:', JSON.stringify(testResultResponse.data, null, 2));
        return;
      }

      // Find the test result for this specific test case
      const testResult = testResults.find((result: any) =>
        result.testCase?.id === testData.testCaseId || result.testCase?.tcId === parseInt(testData.tcId)
      );

      if (!testResult) {
        console.log(`No test result found for test case ${testData.testCaseId} in test run`);
        return;
      }

      testResultId = testResult.id;
      uploadUrl = `${backendUrl}/projects/${projectId}/test-runs/${testData.testRunId}/test-results/${testResultId}/video`;

      console.log(`Uploading video for test run result ID: ${testResultId}`);

      // Read the video file
      const videoBuffer = fs.readFileSync(videoFile);

      // Create form data for video upload
      const formData = new FormData();
      formData.append('file', videoBuffer, {
        filename: 'video.webm',
        contentType: 'video/webm'
      });

      // Upload video to backend using the correct endpoint
      await axios.post(uploadUrl, formData, {
        headers: {
          ...formData.getHeaders(),
          'Authorization': `Bearer ${token}`
        },
        timeout: 30000, // 30 second timeout for video upload
        maxContentLength: 100 * 1024 * 1024, // 100MB max
        maxBodyLength: 100 * 1024 * 1024
      });

      console.log(`Video uploaded successfully for test result: ${testResultId}`);

      

    } catch (error) {
      console.error('Failed to upload test video:', error);

      // Log more detailed error information
      if (axios.isAxiosError(error) && error.response) {
        console.error('Video upload response status:', error.response.status);
        console.error('Video upload response data:', error.response.data);
        console.error('Video upload request URL:', error.config?.url);
      } else if (axios.isAxiosError(error) && error.request) {
        console.error('Video upload - no response received');
        console.error('Video upload request URL:', error.config?.url);
      } else {
        console.error('Video upload error message:', (error as Error).message);
      }
    }
  }

  // Upload test screenshot to backend
  private static async uploadTestScreenshot(testData: any, authToken: string): Promise<void> {
    try {
      const fs = require('fs');
      const path = require('path');
      const FormData = require('form-data');

      // Look for screenshot files in test-results directory
      const testResultsDir = path.join(process.cwd(), 'test-results');

      if (!fs.existsSync(testResultsDir)) {
        console.log('No test-results directory found, skipping screenshot upload');
        return;
      }

      // Find screenshot files - look for patterns like master-{title}/screenshot.png
      const testTitle = testData.testCase?.title || 'Unknown';
      const sanitizedTitle = testTitle.replace(/[^a-zA-Z0-9\s-]/g, '').replace(/\s+/g, '-');

      // Try multiple possible screenshot paths
      const possibleScreenshotPaths = [
        path.join(testResultsDir, `master-${sanitizedTitle}`, 'screenshot.png'),
        path.join(testResultsDir, `master-${testData.tcId}`, 'screenshot.png'),
        path.join(testResultsDir, `master-test`, 'screenshot.png')
      ];

      // Also search for any screenshot files in subdirectories
      const findScreenshotFiles = (dir: string): string[] => {
        const screenshotFiles: string[] = [];
        console.log(`Searching for screenshots in directory: ${dir}`);
        try {
          const items = fs.readdirSync(dir);
          console.log(`Directory contents: ${items.join(', ')}`);
          for (const item of items) {
            const fullPath = path.join(dir, item);
            const stat = fs.statSync(fullPath);
            if (stat.isDirectory()) {
              console.log(`Recursing into subdirectory: ${item}`);
              screenshotFiles.push(...findScreenshotFiles(fullPath));
            } else if (item.match(/(screenshot|test-finished|test-failed).*\.(png|jpg|jpeg)$/i)) {
              console.log(`Found matching screenshot file: ${item}`);
              screenshotFiles.push(fullPath);
            } else {
              console.log(`File ${item} does not match screenshot pattern`);
            }
          }
        } catch (error) {
          console.error('Error reading directory for screenshots:', error);
        }
        console.log(`Returning ${screenshotFiles.length} screenshot files from ${dir}`);
        return screenshotFiles;
      };

      // Get all screenshot files
      const allScreenshotFiles = findScreenshotFiles(testResultsDir);
      console.log('Found screenshot files:', allScreenshotFiles);
      console.log('Checking specific paths:', possibleScreenshotPaths);

      // Add the specific paths we're looking for
      const screenshotFiles = [...possibleScreenshotPaths.filter(p => fs.existsSync(p)), ...allScreenshotFiles];
      console.log('Combined screenshot files:', screenshotFiles);

      if (screenshotFiles.length === 0) {
        console.log('No screenshot files found for upload');
        return;
      }

      // Use the most recent screenshot file
      const screenshotFile = screenshotFiles.sort((a, b) => {
        const statA = fs.statSync(a);
        const statB = fs.statSync(b);
        return statB.mtime.getTime() - statA.mtime.getTime();
      })[0];

      console.log(`Found screenshot file for upload: ${screenshotFile}`);

      // First, get the test result ID from the backend
      const backendUrl = process.env.AGENTQ_API_URL || 'http://localhost:3010';

      // Use the provided auth token for backend authentication
      console.log('Using auth token for screenshot upload authentication');
      const token = authToken;

      let testResultId: string;
      let uploadUrl: string;

      // Always use test run endpoints for websocket_ai_test_run
      // For test runs, get the test result from the test run endpoint
      const projectId = testData.projectId || testData.testCase?.projectId;

      console.log(`Fetching test results for screenshot from: ${backendUrl}/projects/${projectId}/test-runs/${testData.testRunId}/test-results`);
      console.log(`Using projectId: ${projectId}, testRunId: ${testData.testRunId}`);

      const testResultResponse = await axios.get(
        `${backendUrl}/projects/${projectId}/test-runs/${testData.testRunId}/test-results`,
        {
          headers: {
            'Authorization': `Bearer ${token}`
          },
          timeout: 5000,
          params: {
            limit: 50,
            sortField: 'createdAt',
            sortDirection: 'DESC'
          }
        }
      );

      // Handle different API response structures (results vs data)
      const testResults = testResultResponse.data.results || testResultResponse.data.data || testResultResponse.data;

      if (!testResults || testResults.length === 0) {
        console.log('No test results found in test run for screenshot upload');
        console.log('API response structure:', JSON.stringify(testResultResponse.data, null, 2));
        return;
      }

      // Find the test result for this specific test case
      const testResult = testResults.find((result: any) =>
        result.testCase?.id === testData.testCaseId || result.testCase?.tcId === parseInt(testData.tcId)
      );

      if (!testResult) {
        console.log(`No test result found for test case ${testData.testCaseId} in test run`);
        return;
      }

      testResultId = testResult.id;
      uploadUrl = `${backendUrl}/projects/${projectId}/test-runs/${testData.testRunId}/test-results/${testResultId}/screenshot`;

      console.log(`Uploading screenshot for test run result ID: ${testResultId}`);

      // Read the screenshot file
      const screenshotBuffer = fs.readFileSync(screenshotFile);

      // Create form data for screenshot upload
      const formData = new FormData();
      formData.append('file', screenshotBuffer, {
        filename: 'screenshot.png',
        contentType: 'image/png'
      });

      // Upload screenshot to backend using the correct endpoint
      await axios.post(uploadUrl, formData, {
        headers: {
          ...formData.getHeaders(),
          'Authorization': `Bearer ${token}`
        },
        timeout: 30000, // 30 second timeout for screenshot upload
        maxContentLength: 10 * 1024 * 1024, // 10MB max
        maxBodyLength: 10 * 1024 * 1024
      });

      console.log(`Screenshot uploaded successfully for test result: ${testResultId}`);

      // Clean up the screenshot file after successful upload
      try {
        fs.unlinkSync(screenshotFile);
        console.log(`Cleaned up screenshot file: ${screenshotFile}`);
      } catch (cleanupError) {
        console.error('Failed to clean up screenshot file:', cleanupError);
      }

    } catch (error) {
      console.error('Failed to upload test screenshot:', error);

      // Log more detailed error information
      if (axios.isAxiosError(error) && error.response) {
        console.error('Screenshot upload response status:', error.response.status);
        console.error('Screenshot upload response data:', error.response.data);
        console.error('Screenshot upload request URL:', error.config?.url);
      } else if (axios.isAxiosError(error) && error.request) {
        console.error('Screenshot upload - no response received');
        console.error('Screenshot upload request URL:', error.config?.url);
      } else {
        console.error('Screenshot upload error message:', (error as Error).message);
      }
    }
  }

  // Add a method to handle test execution with retries
  static async executeTestWithRetries(apiKey: string, testData: any, maxRetries = 3): Promise<void> {
    let retries = 0;
    
    while (retries <= maxRetries) {
      try {
        await this.executeTest(apiKey, testData);
        return; // Success, exit the loop
      } catch (error) {
        retries++;
        
        if (retries > maxRetries) {
          // Max retries reached, rethrow the error
          throw error;
        }
        
        // Wait before retrying
        const delay = 1000 * Math.pow(2, retries - 1); // Exponential backoff
        console.log(`Test execution failed, retrying in ${delay}ms (${retries}/${maxRetries})`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }
}

// Initialize the heartbeat checker
TestRunnerService.startHeartbeatChecker();
