import { Entity, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';

export enum TempTestResultStatus {
  PASSED = 'passed',
  FAILED = 'failed',
  RUNNING = 'running',
  CANCELLED = 'cancelled'
}

@Entity('temp_test_results')
export class TempTestResult {
  @ApiProperty({
    description: 'The unique identifier of the temp test result',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({
    description: 'Test case ID',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @Column()
  testCaseId: string;

  @ApiProperty({
    description: 'Test case sequential ID',
    example: 'TC-001'
  })
  @Column()
  tcId: string;

  @ApiProperty({
    description: 'Project ID',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @Column()
  projectId: string;

  @ApiProperty({
    description: 'Test execution status',
    enum: TempTestResultStatus,
    example: TempTestResultStatus.PASSED
  })
  @Column({
    type: 'enum',
    enum: TempTestResultStatus,
    default: TempTestResultStatus.RUNNING
  })
  status: TempTestResultStatus;

  @ApiProperty({
    description: 'Test execution summary',
    example: 'Test completed successfully with 5 steps'
  })
  @Column('text', { nullable: true })
  summary: string;

  @ApiProperty({
    description: 'Test execution duration in milliseconds',
    example: 15000
  })
  @Column('int', { nullable: true })
  duration: number;

  @ApiProperty({
    description: 'Google Cloud Storage URL for full logs',
    example: 'gs://agentq-test-logs/test-123/logs.json'
  })
  @Column({ nullable: true })
  logsUrl: string;

  @ApiProperty({
    description: 'Google Cloud Storage URL for test execution video',
    example: 'gs://agentq-test-logs/test-123/video.webm'
  })
  @Column({ nullable: true })
  videoUrl: string;

  @ApiProperty({
    description: 'Google Cloud Storage URL for test execution screenshot',
    example: 'gs://agentq-test-logs/test-123/screenshot.png'
  })
  @Column({ nullable: true })
  screenshotUrl: string;

  @ApiProperty({
    description: 'Test data used for execution',
    example: '{"steps": [...], "testCase": {...}}'
  })
  @Column('jsonb', { nullable: true })
  testData: any;

  @ApiProperty({
    description: 'Error message if test failed',
    example: 'Element not found: #login-button'
  })
  @Column('text', { nullable: true })
  errorMessage: string;

  @ApiProperty({
    description: 'When the test was executed',
    example: '2024-02-20T12:00:00Z'
  })
  @Column({ type: 'timestamp', nullable: true })
  executedAt: Date;

  @ApiProperty({
    description: 'When the record was created',
    example: '2024-02-20T12:00:00Z'
  })
  @CreateDateColumn()
  createdAt: Date;

  @ApiProperty({
    description: 'When the record was last updated',
    example: '2024-02-20T12:00:00Z'
  })
  @UpdateDateColumn()
  updatedAt: Date;
}
