import { Injectable, Logger } from '@nestjs/common';
import { Storage } from '@google-cloud/storage';

@Injectable()
export class StorageService {
  private storage: Storage;
  private logger = new Logger(StorageService.name);
  private bucketName: string;

  constructor() {
    // Initialize Google Cloud Storage
    this.storage = new Storage({
      projectId: process.env.GCP_PROJECT_ID,
      credentials: {
        client_email: process.env.GCP_CLIENT_EMAIL,
        private_key: process.env.GCP_PRIVATE_KEY?.replace(/\\n/g, '\n'),
      },
    });
    this.bucketName = process.env.GCP_BUCKET_NAME || 'agentq-test-logs';
  }

  /**
   * Upload video to Google Cloud Storage in a simple path: videos/{testResultId}/video.webm
   */
  async uploadVideoSimple(
    testResultId: string,
    buffer: Buffer,
    mimetype: string
  ): Promise<string> {
    try {
      const fileName = `test-results/videos/${testResultId}/video.webm`;
      const file = this.storage.bucket(this.bucketName).file(fileName);

      await file.save(buffer, {
        metadata: {
          contentType: mimetype,
        },
      });

      // Return the GCS URL (use signed URLs for access)
      return `gs://${this.bucketName}/${fileName}`;
    } catch (error) {
      console.error('Error uploading simple video to GCS:', error);
      throw error;
    }
  }

  /**
   * Upload video to Google Cloud Storage
   */
  async uploadVideo(
    projectId: string,
    testRunId: string,
    testCaseId: string,
    resultId: string,
    buffer: Buffer,
    mimetype: string
  ): Promise<string> {
    try {
      const fileName = `videos/${projectId}/${testRunId}/${testCaseId}/${resultId}/video.webm`;
      const file = this.storage.bucket(this.bucketName).file(fileName);

      await file.save(buffer, {
        metadata: {
          contentType: mimetype,
        },
      });

      // Return the GCS URL (use signed URLs for access)
      return `gs://${this.bucketName}/${fileName}`;
    } catch (error) {
      console.error('Error uploading video to GCS:', error);
      throw error;
    }
  }

  /**
   * Upload screenshot to Google Cloud Storage (simple path: screenshots/{testResultId}/screenshot.png)
   */
  async uploadScreenshotSimple(
    testResultId: string,
    buffer: Buffer,
    mimetype: string
  ): Promise<string> {
    try {
      const fileName = `test-results/screenshots/${testResultId}/screenshot.png`;
      const file = this.storage.bucket(this.bucketName).file(fileName);

      await file.save(buffer, {
        metadata: {
          contentType: mimetype,
        },
      });

      // Return the GCS URL (use signed URLs for access)
      return `gs://${this.bucketName}/${fileName}`;
    } catch (error) {
      console.error('Error uploading simple screenshot to GCS:', error);
      throw error;
    }
  }

  /**
   * Upload screenshot to Google Cloud Storage
   */
  async uploadScreenshot(
    projectId: string,
    testRunId: string,
    testCaseId: string,
    resultId: string,
    buffer: Buffer,
    mimetype: string
  ): Promise<string> {
    try {
      const fileName = `screenshots/${projectId}/${testRunId}/${testCaseId}/${resultId}/screenshot.png`;
      const file = this.storage.bucket(this.bucketName).file(fileName);

      await file.save(buffer, {
        metadata: {
          contentType: mimetype,
        },
      });

      // Return the GCS URL (use signed URLs for access)
      return `gs://${this.bucketName}/${fileName}`;
    } catch (error) {
      console.error('Error uploading screenshot to GCS:', error);
      throw error;
    }
  }

  /**
   * Upload logs to Google Cloud Storage
   */
  async uploadLogs(uploadData: any, logs: string[]): Promise<string> {
    try {
      const fileName = `test-results/logs/${uploadData.testResultId}/logs.json`;
      const file = this.storage.bucket(this.bucketName).file(fileName);

      const logData = {
        timestamp: new Date().toISOString(),
        logs,
        metadata: {
          totalLogs: logs.length,
          uploadedAt: new Date().toISOString(),
          ...uploadData
        },
      };

      // Upload the logs as JSON
      await file.save(JSON.stringify(logData, null, 2), {
        metadata: {
          contentType: 'application/json',
        },
      });

      // Return the GCS URL (use signed URLs for access)
      return `gs://${this.bucketName}/${fileName}`;
    } catch (error) {
      console.error('Error uploading logs to GCS:', error);
      throw error;
    }
  }

  /**
   * Delete all test run data from Google Cloud Storage
   * @param projectId - The project ID
   * @param testRunId - The test run ID to delete
   * @returns Promise<void>
   */
  async deleteTestRunData(projectId: string, testRunId: string): Promise<void> {
    try {
      this.logger.log(`Starting cleanup of test run data for: ${testRunId}`);

      // Define the prefixes for all test run related files
      const prefixes = [
        `test-results/videos/${projectId}/${testRunId}/`,
        `test-results/screenshots/${projectId}/${testRunId}/`,
        `test-results/logs/${projectId}/${testRunId}/`
      ];

      let totalDeleted = 0;

      // Delete files for each prefix
      for (const prefix of prefixes) {
        try {
          this.logger.log(`Deleting files with prefix: ${prefix}`);

          // List all files with this prefix
          const [files] = await this.storage.bucket(this.bucketName).getFiles({
            prefix: prefix
          });

          if (files.length > 0) {
            this.logger.log(`Found ${files.length} files to delete with prefix: ${prefix}`);

            // Delete all files
            await Promise.all(files.map(file => file.delete()));
            totalDeleted += files.length;

            this.logger.log(`Successfully deleted ${files.length} files with prefix: ${prefix}`);
          } else {
            this.logger.log(`No files found with prefix: ${prefix}`);
          }
        } catch (error) {
          this.logger.error(`Error deleting files with prefix ${prefix}:`, error);
          // Continue with other prefixes even if one fails
        }
      }

      this.logger.log(`Test run cleanup completed. Total files deleted: ${totalDeleted}`);
    } catch (error) {
      this.logger.error(`Failed to cleanup test run data for ${testRunId}:`, error);
      throw new Error(`Failed to cleanup test run data: ${error}`);
    }
  }

  /**
   * Get a signed URL for a file in Google Cloud Storage or handle local storage URLs
   * @param gcsUrl - The GCS URL (gs://bucket-name/path/to/file) or local storage indicator
   * @returns Promise<string> - The signed URL for direct access or local indicator
   */
  async getSignedUrl(gcsUrl: string): Promise<string> {
    // If it's a local storage indicator, return a placeholder or error message
    if (gcsUrl.startsWith('local://')) {
      this.logger.log(`File is stored locally, cannot generate signed URL: ${gcsUrl}`);
      throw new Error('File is stored locally - Google Cloud Storage not configured');
    }

    if (!this.storage) {
      this.logger.error('Google Cloud Storage not available for generating signed URL');
      throw new Error('Google Cloud Storage not configured');
    }

    try {
      // Extract file path from GCS URL
      const filePath = gcsUrl.replace(`gs://${this.bucketName}/`, '');
      const file = this.storage.bucket(this.bucketName).file(filePath);

      // Generate a signed URL that expires in 1 hour
      const [signedUrl] = await file.getSignedUrl({
        version: 'v4',
        action: 'read',
        expires: Date.now() + 60 * 60 * 1000, // 1 hour
      });

      return signedUrl;
    } catch (error) {
      this.logger.error(`Failed to generate signed URL for ${gcsUrl}:`, error);
      throw new Error(`Failed to generate signed URL: ${error}`);
    }
  }
}
