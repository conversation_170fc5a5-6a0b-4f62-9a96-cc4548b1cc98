import { forwardRef, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TestResultsController } from './test-results.controller';
import { TestResultsService } from './test-results.service';
import { StorageService } from './storage.service';
import { TestResult } from './test-result.entity';
import { TestRunModule } from '../test-runs/test-run.module';

@Module({
  imports: [TypeOrmModule.forFeature([TestResult]), forwardRef(() => TestRunModule)],
  controllers: [TestResultsController],
  providers: [TestResultsService, StorageService],
  exports: [TestResultsService, StorageService],
})
export class TestResultsModule {}
