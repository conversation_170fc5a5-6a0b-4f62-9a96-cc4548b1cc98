import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { TestResult } from './test-result.entity';
import { Storage } from '@google-cloud/storage';

@Injectable()
export class TestResultsService {
  private storage: Storage;
  private bucketName: string;

  constructor(
    @InjectRepository(TestResult)
    private testResultsRepository: Repository<TestResult>,
  ) {
    // Initialize Google Cloud Storage
    this.storage = new Storage({
      projectId: process.env.GCP_PROJECT_ID,
      credentials: {
        client_email: process.env.GCP_CLIENT_EMAIL,
        private_key: process.env.GCP_PRIVATE_KEY?.replace(/\\n/g, '\n'),
      },
    });
    this.bucketName = process.env.GCP_BUCKET_NAME || 'agentq-test-logs';
  }

  /**
   * Create a new test result (simple, no project/testRun required)
   */
  async createSimpleTestResult(
    createDto: { id?: string; testRunId: string; testCaseId: string; status?: string }
  ): Promise<{ success: boolean; id: string }> {
    // Import the TestResultStatus enum
    const statusEnum = (this.testResultsRepository.metadata.columns.find(col => col.propertyName === 'status')?.enum || ['untested'])[0];
    const entity = this.testResultsRepository.create({
      testRunId: createDto.testRunId,
      testCaseId: createDto.testCaseId,
      status: (createDto.status as any) || statusEnum || 'untested'
    });
    await this.testResultsRepository.save(entity);
    return { success: true, id: entity.id };
  }

  /**
   * Patch videoUrl and/or screenshotUrl for a test result by testResultId
   */
  async patchTestResult(
    testResultId: string,
    updateDto: { videoUrl?: string; screenshotUrl?: string }
  ): Promise<{ success: boolean }> {
    const updateFields: any = {};
    if (updateDto.videoUrl !== undefined) updateFields.videoUrl = updateDto.videoUrl;
    if (updateDto.screenshotUrl !== undefined) updateFields.screenshotUrl = updateDto.screenshotUrl;

    if (Object.keys(updateFields).length === 0) {
      throw new Error('No fields to update');
    }

    try {
      const result = await this.testResultsRepository.update(
        { id: testResultId },
        updateFields
      );

      if (result.affected && result.affected > 0) {
        return { success: true };
      } else {
        console.error(`patchTestResult: No test result found for id=${testResultId}`);
        throw new Error('Test result not found or not updated');
      }
    } catch (err) {
      console.error(`patchTestResult: Error updating testResultId=${testResultId}`, err);
      throw err;
    }
  }

  /**
   * Update test result with video URL
   */
  async updateTestResultVideoUrl(resultId: string, videoUrl: string): Promise<void> {
    await this.testResultsRepository.update(resultId, { videoUrl });
  }

  /**
   * Update test result with screenshot URL
   */
  async updateTestResultScreenshotUrl(resultId: string, screenshotUrl: string): Promise<void> {
    await this.testResultsRepository.update(resultId, { screenshotUrl });
  }

  /**
   * Update test result with logs URL
   */
  async updateTestResultLogsUrl(resultId: string, logsUrl: string): Promise<void> {
    await this.testResultsRepository.update(resultId, { logsUrl });
  }

  /**
   * Find a test result by ID, projectId, and testRunId
   */
  async findByIdAndProjectAndTestRun(resultId: string, projectId: string, testRunId: string): Promise<TestResult | undefined> {
    return this.testResultsRepository
      .createQueryBuilder('testResult')
      .leftJoinAndSelect('testResult.testRun', 'testRun')
      .leftJoinAndSelect('testResult.testCase', 'testCase')
      .where('testResult.id = :resultId', { resultId })
      .andWhere('testRun.id = :testRunId', { testRunId })
      .andWhere('testRun.projectId = :projectId', { projectId })
      .getOne();
  }
}
