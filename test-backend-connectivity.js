#!/usr/bin/env node

/**
 * Simple script to test backend connectivity and API endpoints
 */

const axios = require('axios');

const BACKEND_URL = 'http://localhost:3010';

async function testBackendConnectivity() {
  console.log('🔍 Testing backend connectivity...');
  
  // Test 1: Basic connectivity
  try {
    console.log('\n1. Testing basic connectivity...');
    const response = await axios.get(`${BACKEND_URL}/health`, { timeout: 5000 });
    console.log('✅ Health check passed:', response.status);
    console.log('   Response:', response.data);
  } catch (error) {
    console.error('❌ Health check failed:', error.message);
    if (error.code === 'ECONNREFUSED') {
      console.error('   Backend server is not running on port 3010');
      return;
    }
  }

  // Test 2: Test the specific endpoint that's failing
  const projectId = '77623e97-387e-4f76-877d-4f5fb9b57070';
  const testRunId = '90cbea52-b652-452c-bcae-8031ed011cd2';
  const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************.vdhv6pqSa1gcuQ_vrBF5gsMAAfhbr8xGLpBAc90I3iU';

  try {
    console.log('\n2. Testing JWT token validity...');
    const authResponse = await axios.post(`${BACKEND_URL}/auth/verify`, {}, {
      headers: { 'Authorization': `Bearer ${token}` },
      timeout: 5000
    });
    console.log('✅ JWT token is valid:', authResponse.status);
    console.log('   User info:', authResponse.data);
  } catch (error) {
    console.error('❌ JWT token validation failed:', error.response?.status, error.response?.data || error.message);
  }

  try {
    console.log('\n3. Testing the failing endpoint...');
    const testResultsResponse = await axios.get(
      `${BACKEND_URL}/projects/${projectId}/test-runs/${testRunId}/test-results`,
      {
        headers: { 'Authorization': `Bearer ${token}` },
        timeout: 5000,
        params: {
          limit: 50,
          sortField: 'createdAt',
          sortDirection: 'DESC'
        }
      }
    );
    console.log('✅ Test results endpoint works:', testResultsResponse.status);
    console.log('   Response data:', testResultsResponse.data);
  } catch (error) {
    console.error('❌ Test results endpoint failed:', error.response?.status, error.response?.data || error.message);
    
    if (error.response?.status === 404) {
      console.log('\n🔍 Checking if project exists...');
      try {
        const projectResponse = await axios.get(`${BACKEND_URL}/projects/${projectId}`, {
          headers: { 'Authorization': `Bearer ${token}` },
          timeout: 5000
        });
        console.log('✅ Project exists:', projectResponse.status);
      } catch (projectError) {
        console.error('❌ Project not found:', projectError.response?.status);
      }

      console.log('\n🔍 Checking if test run exists...');
      try {
        const testRunResponse = await axios.get(`${BACKEND_URL}/projects/${projectId}/test-runs/${testRunId}`, {
          headers: { 'Authorization': `Bearer ${token}` },
          timeout: 5000
        });
        console.log('✅ Test run exists:', testRunResponse.status);
      } catch (testRunError) {
        console.error('❌ Test run not found:', testRunError.response?.status);
      }
    }
  }

  // Test 3: Check available projects
  try {
    console.log('\n4. Checking available projects...');
    const projectsResponse = await axios.get(`${BACKEND_URL}/projects`, {
      headers: { 'Authorization': `Bearer ${token}` },
      timeout: 5000
    });
    console.log('✅ Available projects:');
    if (projectsResponse.data && projectsResponse.data.length > 0) {
      projectsResponse.data.forEach(project => {
        console.log(`   - ${project.id}: ${project.name}`);
      });

      // Check test runs for the first project
      const firstProject = projectsResponse.data[0];
      console.log(`\n5. Checking test runs for project: ${firstProject.name}`);
      try {
        const testRunsResponse = await axios.get(`${BACKEND_URL}/projects/${firstProject.id}/test-runs`, {
          headers: { 'Authorization': `Bearer ${token}` },
          timeout: 5000
        });
        console.log('✅ Available test runs:');
        if (testRunsResponse.data && testRunsResponse.data.results && testRunsResponse.data.results.length > 0) {
          testRunsResponse.data.results.forEach(testRun => {
            console.log(`   - ${testRun.id}: ${testRun.name} (status: ${testRun.status})`);
          });
        } else {
          console.log('   No test runs found');
        }
      } catch (testRunError) {
        console.error('❌ Failed to get test runs:', testRunError.response?.status);
      }
    } else {
      console.log('   No projects found');
    }
  } catch (error) {
    console.error('❌ Failed to get projects:', error.response?.status, error.message);
  }
}

testBackendConnectivity().catch(console.error);
