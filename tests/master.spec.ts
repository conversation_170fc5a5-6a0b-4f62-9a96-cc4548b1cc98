import { expect } from '@playwright/test';
import { q, test } from 'agentq_web_automation_test';
import axios from 'axios';

// Load test data from environment
const testDataEnv = process.env.TEST_DATA;
let stepsData: any = null;

if (testDataEnv) {
  try {
    stepsData = JSON.parse(testDataEnv);
    console.log(`Loaded test data for: ${stepsData.testCase.title}`);
  } catch (error) {
    console.error('Failed to parse test data:', error);
  }
}

const BACKEND_URL = process.env.BACKEND_URL || 'http://localhost:3010';

test(stepsData?.testCase?.title || 'Dynamic Test Case', async ({ page }) => {
  console.log('🚀 Starting pure Playwright test execution');

  // Set page context for AgentQ library
  (global as any).currentPage = page;

  // Verify JWT token if available
  const jwtToken = process.env.AGENTQ_JWT_TOKEN || process.env.AUTH_TOKEN;
  if (jwtToken) {
    console.log('🔍 JWT Token available: true');
    console.log('🔍 JWT Token value:', jwtToken.substring(0, 20) + '...');

    try {
      console.log('Verifying JWT token with:', `${BACKEND_URL}/auth/verify`);
      await axios.post(`${BACKEND_URL}/auth/verify`, {}, {
        headers: {
          'Authorization': `Bearer ${jwtToken}`
        }
      });
      console.log('✅ JWT token verification successful');
    } catch (verifyError) {
      console.warn('⚠️ JWT token verification failed:', verifyError);
    }
  }

  if (!stepsData || !stepsData.steps) {
    console.log('⚠️ No test data provided, running simple demo test');
    // Run a simple demo test
    await page.goto('https://example.com');
    await expect(page).toHaveTitle(/Example/);
    console.log('✅ Demo test completed successfully');
    return;
  }

  try {
    console.log(`📋 Executing ${stepsData.steps.length} test steps`);

    // Execute each step
    for (const step of stepsData.steps) {
      if (step.action === 'prompt' && step.value) {
        console.log(`🌐 Step: prompt ${step.value} ✓`);
        await q(step.value);
      } else if (step.action === 'Go to Page' && step.target) {
        console.log(`Step: goto ${step.target} ✓`);
        await page.goto(step.target, { timeout: 30000 });
      } else if (step.action === 'goto' && step.target) {
        console.log(`Step: goto ${step.target} ✓`);
        await page.goto(step.target, { timeout: 30000 });
      } else if (step.action === 'navigate' && step.target) {
        console.log(`Step: navigate ${step.target} ✓`);
        await page.goto(step.target, { timeout: 30000 });
      } else if (step.action === 'Fill' && step.target && step.value) {
        console.log(`Step: fill ${step.target} ${step.value} ✓`);
        await page.fill(step.target, step.value);
      } else if (step.action === 'write' && step.target && step.value) {
        console.log(`Step: write ${step.target} ${step.value} ✓`);
        await page.fill(step.target, step.value);
      } else if (step.action === 'Click' && step.target) {
        console.log(`Step: click ${step.target} ✓`);
        await page.click(step.target);
      } else if (step.action === 'click' && step.target) {
        console.log(`Step: click ${step.target} ✓`);
        await page.click(step.target);
      } else if (step.action === 'assertText' && step.target && step.value) {
        console.log(`Step: assertText ${step.target} ${step.value} ✓`);
        await expect(page.locator(step.target)).toHaveText(step.value);
      } else {
        console.log(`Step: ${step.action} - Skipped (unsupported action)`);
      }
    }

    console.log('✅ All test steps completed successfully');

  } catch (error) {
    console.error('❌ Test failed:', error);
    throw error;
  }
});
